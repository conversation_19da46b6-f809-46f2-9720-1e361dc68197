#include "com_linux_user.hpp"
#include "cc/target/common/inc/linux_svs_interface.hpp"
#include "cc/target/common/inc/tic/timestamp_types.hpp"
#include "cc/target/linux/inc/CustomInterfaceSync.h"
#include "cc/util/common/inc/CommonUtil.hpp"
#include "cc/util/logging/inc/LoggingContexts.h"
#include "cc/vhm/inc/vhm.hpp"
#include "cc/views/nfsengineeringview/inc/CpcEngineeringScreen.h"
#include "cc/views/planview/inc/PlanView.h"
#include "pc/generic/rapidjson/document.h"
#include "pc/generic/util/chrono/inc/chrono.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "cc/assets/uielements/inc/TurnArroundOverlay.h"
#include "cc/assets/common/inc/VehicleTransparentHandler.h"
#include "cc/core/src/ViewModeStateTransitionManager.h"
using pc::util::logging::g_AppContext;
using pc::util::logging::g_COMSocketContext;

// for qac
static bool updateLightBool(AVMState f_status)
{
    bool l_result = false;
    if (AVMState::AVM_OPEN == f_status)
    {
        l_result = true;
    }
    return l_result;
}

static bool parseJsonArray(const std::string& jsonStr, pc::c2w::SatCamArray& f_camArr)
{
    rapidjson::Document document;
    document.Parse(jsonStr.c_str());

    bool l_verifyStatus = true;

    // bool l_leftUpdate  = false; // no use now.
    // bool l_rightUpdate = false;
    // bool l_frontUpdate = false;
    // bool l_rearUpdate  = false;

    if (document.IsArray())
    {
        for (rapidjson::SizeType i = 0; i < document.Size(); ++i)
        {
            const rapidjson::Value& obj = document[i];

            double cx = 0.0;
            double cy = 0.0;
            double fx = 0.0;
            double fy = 0.0;
            double k1 = 0.0;
            double k2 = 0.0;
            double k3 = 0.0;
            double k4 = 0.0;

            if (obj.HasMember("name") && obj["name"].IsString())
            {
                const char* const name = obj["name"].GetString();
                XLOG_ERROR(g_AppContext, "[svs]: parseJsonArray name is " << name );
            }

            if (obj.HasMember("cx") && obj["cx"].IsDouble())
            {
                cx = obj["cx"].GetDouble();
                XLOG_ERROR(g_AppContext, "[svs]: parseJsonArray cx is " << cx );
            }

            if (obj.HasMember("cy") && obj["cy"].IsDouble())
            {
                cy = obj["cy"].GetDouble();
                XLOG_ERROR(g_AppContext, "[svs]: parseJsonArray cy is " << cy );
            }

            if (obj.HasMember("fx") && obj["fx"].IsDouble())
            {
                fx = obj["fx"].GetDouble();
                XLOG_ERROR(g_AppContext, "[svs]: parseJsonArray fx is " << fx );
            }

            if (obj.HasMember("fy") && obj["fy"].IsDouble())
            {
                fy = obj["fy"].GetDouble();
                XLOG_ERROR(g_AppContext, "[svs]: parseJsonArray fy is " << fy );
            }

            if (obj.HasMember("k1") && obj["k1"].IsDouble())
            {
                k1 = obj["k1"].GetDouble();
                XLOG_ERROR(g_AppContext, "[svs]: parseJsonArray k1 is " << k1 );
            }

            if (obj.HasMember("k2") && obj["k2"].IsDouble())
            {
                k2 = obj["k2"].GetDouble();
                XLOG_ERROR(g_AppContext, "[svs]: parseJsonArray k2 is " << k2 );
            }

            if (obj.HasMember("k3") && obj["k3"].IsDouble())
            {
                k3 = obj["k3"].GetDouble();
                XLOG_ERROR(g_AppContext, "[svs]: parseJsonArray k3 is " << k3 );
            }

            if (obj.HasMember("k4") && obj["k4"].IsDouble())
            {
                k4 = obj["k4"].GetDouble();
                XLOG_ERROR(g_AppContext, "[svs]: parseJsonArray k4 is " << k4 );
            }

            //????
            if (obj.HasMember("xi") && obj["xi"].IsDouble())
            {
                const double xi = obj["xi"].GetDouble();
                XLOG_ERROR(g_AppContext, "[svs]: parseJsonArray xi is " << xi );
            }

            if (obj.HasMember("verify_status") && obj["verify_status"].IsBool())
            {
                const bool verify_status = obj["verify_status"].GetBool();
                l_verifyStatus     = (l_verifyStatus == verify_status);
            }

            const pc::c2w::IntrinsicCalibration l_intrinsic(static_cast<float>(fx),
                                                    static_cast<float>(fy),
                                                    static_cast<float>(cx),
                                                    static_cast<float>(cy),
                                                    static_cast<float>(k1),
                                                    static_cast<float>(k2),
                                                    static_cast<float>(k3),
                                                    static_cast<float>(k4));

            switch (i)
            {
            // Front
            case 0:
            {
                // if
                // (isMajorChangeInAngle(f_camArr[pc::core::sysconf::EXT_FRONT_CAMERA].getIntrinsicCalibration(),l_intrinsic))
                // {
                //     l_frontUpdate = true;}
                f_camArr[pc::core::sysconf::EXT_FRONT_CAMERA].setIntrinsicCalibration(l_intrinsic);
                break;
            }
            // Left
            case 1:
            {
                // if
                // (!isMajorChangeInAngle(f_camArr[pc::core::sysconf::EXT_LEFT_CAMERA].getIntrinsicCalibration(),l_intrinsic))
                // {
                //     l_leftUpdate = true;}
                f_camArr[pc::core::sysconf::EXT_LEFT_CAMERA].setIntrinsicCalibration(l_intrinsic);
                f_camArr[pc::core::sysconf::EXT_LEFT_CAMERA_FOLDED].setIntrinsicCalibration(l_intrinsic);
                break;
            }
            // Right
            case 2:
            {
                // if
                // (!isMajorChangeInAngle(f_camArr[pc::core::sysconf::EXT_RIGHT_CAMERA].getIntrinsicCalibration(),l_intrinsic))
                // {
                //     l_rightUpdate = true;}
                f_camArr[pc::core::sysconf::EXT_RIGHT_CAMERA].setIntrinsicCalibration(l_intrinsic);
                f_camArr[pc::core::sysconf::EXT_RIGHT_CAMERA_FOLDED].setIntrinsicCalibration(l_intrinsic);
                break;
            }
            // Rear
            case 3:
            {
                // if
                // (!isMajorChangeInAngle(f_camArr[pc::core::sysconf::EXT_REAR_CAMERA].getIntrinsicCalibration(),l_intrinsic))
                // {
                //     l_rearUpdate = true;}
                f_camArr[pc::core::sysconf::EXT_REAR_CAMERA].setIntrinsicCalibration(l_intrinsic);
                break;
            }
            default:
            {
                break;
            }
            }
        }

        // bool l_updateMajorIntrisicChange = l_verifyStatus && (l_rearUpdate || l_rightUpdate || l_leftUpdate ||
        // l_frontUpdate);

        // if (l_verifyStatus == true && l_updateMajorIntrisicChange == false)
        // {
        //     XLOG_ERROR(g_AppContext,"[svs]: The intrinsic data received is same!!!!!! " );
        // }
        return l_verifyStatus;
    }
    else
    {
        return false;
    }
}

// Define member functions through class name and scope
namespace svs
{

int com_linux_user::init(std::shared_ptr<ServiceCallBack> callBack, std::shared_ptr<ServiceConfig> cfg)
{
    // These valuse will be init at the beginning of app, so we can direct use global values, not thourgh daddy
    g_dataContainerToSvs.m_vehicleInfo.externalParamPath = "/" + cfg->mVehicleConfig->calibPath;
    XLOG_ERROR(g_AppContext, "-----------------set externalParamPath = "
                               << g_dataContainerToSvs.m_vehicleInfo.externalParamPath );
    g_dataContainerToSvs.m_vehicleInfo.commonPath = "/" + cfg->mVehicleConfig->assetsPath;
    XLOG_ERROR(g_AppContext, "-----------------set commonPath = " << g_dataContainerToSvs.m_vehicleInfo.commonPath
                               );
    g_dataContainerToSvs.m_vehicleInfo.subVehicleType = cfg->mVehicleConfig->subVehicleType;
    g_dataContainerToSvs.m_vehicleInfo.vehicleType    = cfg->mVehicleConfig->vehicleType;

    g_dataContainerToSvs.m_StrippedCpjValData.m_cpcDumpImagePath = "/" + cfg->mVehicleConfig->calibPath;

    if (cfg->mVehicleConfig->vehicleType == "SGHL")
    {
        g_dataContainerToSvs.m_vehicleInfo.vehicleType    = "sghl";
        g_dataContainerToSvs.m_vehicleInfo.subVehicleType = "sghl";
        g_dataContainerToSvs.m_vehicleInfo.configPath = cfg->mVehicleConfig->assetsPath + "/cc/yangwang/SGHL/";
        XLOG_ERROR(g_AppContext, "-----------------set configPath = " << g_dataContainerToSvs.m_vehicleInfo.configPath
                               );
    }
    else if (cfg->mVehicleConfig->vehicleType == "SGHC")
    {
        g_dataContainerToSvs.m_vehicleInfo.vehicleType    = "sghc";
        g_dataContainerToSvs.m_vehicleInfo.subVehicleType = "sghc";
        g_dataContainerToSvs.m_vehicleInfo.configPath = cfg->mVehicleConfig->assetsPath + "/cc/yangwang/SGHC/";
        XLOG_ERROR(g_AppContext, "-----------------set configPath = " << g_dataContainerToSvs.m_vehicleInfo.configPath
                               );
    }
    else if (cfg->mVehicleConfig->vehicleType == "ST2")
    {
        g_dataContainerToSvs.m_vehicleInfo.vehicleType    = "st2";
        g_dataContainerToSvs.m_vehicleInfo.subVehicleType = "st2";
        g_dataContainerToSvs.m_vehicleInfo.configPath = cfg->mVehicleConfig->assetsPath + "/cc/dynasty/ST2/";
        XLOG_ERROR(g_AppContext, "-----------------set configPath = " << g_dataContainerToSvs.m_vehicleInfo.configPath
                               );
    }
    else
    {

    }
    // For bench testing
    //  g_dataContainerToSvs.m_vehicleInfo.vehicleType       = "sghl";

    XLOG_ERROR(g_AppContext, "[svs]: vehicleType is !!!!!!!!!!!!****202502222135----------------------"
                               << g_dataContainerToSvs.m_vehicleInfo.vehicleType );

    return static_cast<int>(InitFeedBack::Success);
}

// Implement deInit function
int com_linux_user::deInit()
{
    // TODO: Implement this function
    return static_cast<int>(DeInitFeedBack::Failed_Can_Not_Call);
}

// pCameraParam: Camera intrinsic parameter cache start pointer, arranged in left-right-front-rear order
// size: Number of camera intrinsic parameter caches, usually four
int com_linux_user::setCameraIntrinsicParameters(CameraParameter* pCameraParam, int size)
{
    if (nullptr == pCameraParam)
    {
        return 0;
    }
    vfc::float32_t                cx = (*pCameraParam).cx;
    vfc::float32_t                cy = (*pCameraParam).cy;
    vfc::float32_t                fx = (*pCameraParam).fx;
    vfc::float32_t                fy = (*pCameraParam).fy;
    vfc::float32_t                k1 = (*pCameraParam).k1;
    vfc::float32_t                k2 = (*pCameraParam).k2;
    vfc::float32_t                k3 = (*pCameraParam).k3;
    vfc::float32_t                k4 = (*pCameraParam).k4;
    const pc::c2w::IntrinsicCalibration l_intrinsic(fx, fy, cx, cy, k1, k2, k3, k4);
    g_dataContainerToSvs.m_StrippedCpjValData.m_cameraPara[pc::core::sysconf::EXT_LEFT_CAMERA].setIntrinsicCalibration(
        l_intrinsic);
    g_dataContainerToSvs.m_StrippedCpjValData.m_cameraPara[pc::core::sysconf::EXT_LEFT_CAMERA_FOLDED]
        .setIntrinsicCalibration(l_intrinsic);

    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.left cx is !!" << cx );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.left cy is !!" << cy );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.left fx is !!" << fx );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.left fy is !!" << fy );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.left k1 is !!" << k1 );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.left k2 is !!" << k2 );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.left k3 is !!" << k3 );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.left k4 is !!" << k4 );

    cx = (*(pCameraParam + 1)).cx;
    cy = (*(pCameraParam + 1)).cy;
    fx = (*(pCameraParam + 1)).fx;
    fy = (*(pCameraParam + 1)).fy;
    k1 = (*(pCameraParam + 1)).k1;
    k2 = (*(pCameraParam + 1)).k2;
    k3 = (*(pCameraParam + 1)).k3;
    k4 = (*(pCameraParam + 1)).k4;
    const pc::c2w::IntrinsicCalibration l_intrinsic1(fx, fy, cx, cy, k1, k2, k3, k4);
    g_dataContainerToSvs.m_StrippedCpjValData.m_cameraPara[pc::core::sysconf::EXT_RIGHT_CAMERA].setIntrinsicCalibration(
        l_intrinsic1);
    g_dataContainerToSvs.m_StrippedCpjValData.m_cameraPara[pc::core::sysconf::EXT_RIGHT_CAMERA_FOLDED]
        .setIntrinsicCalibration(l_intrinsic1);

    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.right cx is !!" << cx );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.right cy is !!" << cy );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.right fx is !!" << fx );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.right fy is !!" << fy );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.right k1 is !!" << k1 );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.right k2 is !!" << k2 );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.right k3 is !!" << k3 );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.right k4 is !!" << k4 );

    cx = (*(pCameraParam + 2)).cx;
    cy = (*(pCameraParam + 2)).cy;
    fx = (*(pCameraParam + 2)).fx;
    fy = (*(pCameraParam + 2)).fy;
    k1 = (*(pCameraParam + 2)).k1;
    k2 = (*(pCameraParam + 2)).k2;
    k3 = (*(pCameraParam + 2)).k3;
    k4 = (*(pCameraParam + 2)).k4;
    const pc::c2w::IntrinsicCalibration l_intrinsic2(fx, fy, cx, cy, k1, k2, k3, k4);
    g_dataContainerToSvs.m_StrippedCpjValData.m_cameraPara[pc::core::sysconf::EXT_FRONT_CAMERA].setIntrinsicCalibration(
        l_intrinsic2);

    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.front cx is !!" << cx );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.front cy is !!" << cy );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.front fx is !!" << fx );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.front fy is !!" << fy );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.front k1 is !!" << k1 );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.front k2 is !!" << k2 );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.front k3 is !!" << k3 );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.front k4 is !!" << k4 );

    cx = (*(pCameraParam + 3)).cx;
    cy = (*(pCameraParam + 3)).cy;
    fx = (*(pCameraParam + 3)).fx;
    fy = (*(pCameraParam + 3)).fy;
    k1 = (*(pCameraParam + 3)).k1;
    k2 = (*(pCameraParam + 3)).k2;
    k3 = (*(pCameraParam + 3)).k3;
    k4 = (*(pCameraParam + 3)).k4;
    const pc::c2w::IntrinsicCalibration l_intrinsic3(fx, fy, cx, cy, k1, k2, k3, k4);
    g_dataContainerToSvs.m_StrippedCpjValData.m_cameraPara[pc::core::sysconf::EXT_REAR_CAMERA].setIntrinsicCalibration(
        l_intrinsic3);

    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.rear cx is !!" << cx );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.rear cy is !!" << cy );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.rear fx is !!" << fx );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.rear fy is !!" << fy );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.rear k1 is !!" << k1 );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.rear k2 is !!" << k2 );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.rear k3 is !!" << k3 );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.rear k4 is !!" << k4 );

    g_dataContainerToSvs.m_StrippedCpjValData.m_cameraParaFlag   = true;
    g_dataContainerToSvs.m_StrippedCpjValData.m_intrinsicUpdated = true;
    return 0;
}

void com_linux_user::setCarColor(CarColor carColor)
{
    XLOG_ERROR(g_AppContext, "[svs]: setCarColor is !!" << static_cast<int>(carColor) );
    switch (carColor)
    {
    case CarColor::CAMOUFLAGE_GREEN:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehColorReq =
            static_cast<vfc::uint8_t>(0x69); // HERMES_GREEN
        break;
    }
    case CarColor::DEMON_BLACK:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehColorReq =
            static_cast<vfc::uint8_t>(0x68); // YAO_SHI_BLACK
        break;
    }
    case CarColor::SNOW_WHITE:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehColorReq =
            static_cast<vfc::uint8_t>(0x04); // SNOWY_WHITE
        break;
    }
    case CarColor::SUNRISE_GOLD:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehColorReq =
            static_cast<vfc::uint8_t>(0x27); // SUNRISE_GOLD
        break;
    }
    case CarColor::BLACK_GOLD:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehColorReq =
            static_cast<vfc::uint8_t>(0x6A); // BLACK_GOLD
        break;
    }
    case CarColor::SGHC_HERMES_GREEN:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehColorReq =
            static_cast<vfc::uint8_t>(0x6B); // SGHC_HERMES_GREEN
        break;
    }
    case CarColor::SGHC_OBSIDIAN_BLACK:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehColorReq =
            static_cast<vfc::uint8_t>(0x6C); // SGHC_OBSIDIAN_BLACK
        break;
    }
    case CarColor::SGHC_SNOW_WHITHE:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehColorReq =
            static_cast<vfc::uint8_t>(0x6D); // SGHC_SNOW_WHITHE
        break;
    }
    default:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehColorReq =
            static_cast<vfc::uint8_t>(0x69); // HERMES_GREEN
        break;
    }
    }
    return;
}

// Implement setVehicleWheelSpeed function
// direction: wheel speed, unit: RPM
// length = 16, last four bits are timestamp
// 0,1 = front left, 2,3 = front right, 4,5 = rear right, 6,7 = rear left wheel
// 8-11 front left, front right, rear right, rear left validity: 0x0:valid 0x1:invalid
// e.g., m_wheelRotationFL = speed[0]<<8+speed[1]
void com_linux_user::setVehicleWheelSpeed(unsigned char* speed)
{
    g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationFL = speed[0] << 8 + speed[1];
    g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationFR = speed[2] << 8 + speed[3];
    g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationRR = speed[4] << 8 + speed[5];
    g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationRL = speed[6] << 8 + speed[7];

    g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationFLQualifier =
        (speed[4] == 0x0) ? valin::EWheelRotationQualifier::Q_WHEEL_ROTATION_VALID
                          : valin::EWheelRotationQualifier::Q_WHEEL_ROTATION_INVALID;

    g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationFRQualifier =
        (speed[5] == 0x0) ? valin::EWheelRotationQualifier::Q_WHEEL_ROTATION_VALID
                          : valin::EWheelRotationQualifier::Q_WHEEL_ROTATION_INVALID;

    g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationRRQualifier =
        (speed[6] == 0x0) ? valin::EWheelRotationQualifier::Q_WHEEL_ROTATION_VALID
                          : valin::EWheelRotationQualifier::Q_WHEEL_ROTATION_INVALID;

    g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationRLQualifier =
        (speed[7] == 0x0) ? valin::EWheelRotationQualifier::Q_WHEEL_ROTATION_VALID
                          : valin::EWheelRotationQualifier::Q_WHEEL_ROTATION_INVALID;
}

// direction: wheel direction
// length = 12, last four bits are timestamp
// 0~3 front left, front right, rear right, rear left wheel direction 0x0:invalid 0x1:forward 0x2:backward 0x3:stop
// 4~7 front left, front right, rear right, rear left validity: 0x0:valid 0x1:invalid
void com_linux_user::setVehicleWheelDirection(char* direction, int /*length*/)
{
    switch (direction[0]) //*direction
    {
    case 0x0:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFL =
            valin::EWheelDrvDir::WHEEL_DRV_DIR_UNKNOWN;
        g_dataContainerToSvs.m_StrippedPfValData.m_vehicleDrvDir = cc::target::common::EVehicleDrvDir::DRVDIR_VOID;
        break;
    }
    case 0x1:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFL = valin::EWheelDrvDir::WHEEL_DRV_DIR_FWD;
        g_dataContainerToSvs.m_StrippedPfValData.m_vehicleDrvDir = cc::target::common::EVehicleDrvDir::DRVDIR_FWD;
        break;
    }
    case 0x2:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFL = valin::EWheelDrvDir::WHEEL_DRV_DIR_BWD;
        g_dataContainerToSvs.m_StrippedPfValData.m_vehicleDrvDir = cc::target::common::EVehicleDrvDir::DRVDIR_BWD;
        break;
    }
    case 0x3:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFL = valin::EWheelDrvDir::WHEEL_DRV_DIR_STOP;
        g_dataContainerToSvs.m_StrippedPfValData.m_vehicleDrvDir = cc::target::common::EVehicleDrvDir::DRVDIR_VOID;
        break;
    }
    default:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFL =
            valin::EWheelDrvDir::WHEEL_DRV_DIR_UNKNOWN;
        g_dataContainerToSvs.m_StrippedPfValData.m_vehicleDrvDir = cc::target::common::EVehicleDrvDir::DRVDIR_VOID;
        break;
    }
    }

    switch (direction[1]) //*(direction+1)
    {
    case 0x0:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFR =
            valin::EWheelDrvDir::WHEEL_DRV_DIR_UNKNOWN;
        break;
    }
    case 0x1:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFR = valin::EWheelDrvDir::WHEEL_DRV_DIR_FWD;
        break;
    }
    case 0x2:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFR = valin::EWheelDrvDir::WHEEL_DRV_DIR_BWD;
        break;
    }
    case 0x3:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFR = valin::EWheelDrvDir::WHEEL_DRV_DIR_STOP;
        break;
    }
    default:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFR =
            valin::EWheelDrvDir::WHEEL_DRV_DIR_UNKNOWN;
        break;
    }
    }

    switch (direction[2]) //*(direction+2)
    {
    case 0x0:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRR =
            valin::EWheelDrvDir::WHEEL_DRV_DIR_UNKNOWN;
        break;
    }
    case 0x1:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRR = valin::EWheelDrvDir::WHEEL_DRV_DIR_FWD;
        break;
    }
    case 0x2:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRR = valin::EWheelDrvDir::WHEEL_DRV_DIR_BWD;
        break;
    }
    case 0x3:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRR = valin::EWheelDrvDir::WHEEL_DRV_DIR_STOP;
        break;
    }
    default:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRR =
            valin::EWheelDrvDir::WHEEL_DRV_DIR_UNKNOWN;
        break;
    }
    }

    switch (direction[3]) //*(direction+3)
    {
    case 0x0:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRL =
            valin::EWheelDrvDir::WHEEL_DRV_DIR_UNKNOWN;
        break;
    }
    case 0x1:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRL = valin::EWheelDrvDir::WHEEL_DRV_DIR_FWD;
        break;
    }
    case 0x2:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRL = valin::EWheelDrvDir::WHEEL_DRV_DIR_BWD;
        break;
    }
    case 0x3:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRL = valin::EWheelDrvDir::WHEEL_DRV_DIR_STOP;
        break;
    }
    default:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRL =
            valin::EWheelDrvDir::WHEEL_DRV_DIR_UNKNOWN;
        break;
    }
    }

    return;
}

// Implement setLayout function
// int com_linux_user::setLayout(vehicleModelView view, bool showAPA)  {
//     g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_huDisplayID =
//                                                 static_cast<vfc::uint8_t>(view);
//     XLOG_ERROR(g_AppContext,"[svs]: setLayout is " <<static_cast<vfc::int32_t>(view));
//     return 0;
// }

static std::mutex mtx_linux;
// Implement setAvmView function
int com_linux_user::setAvmView(int viewId, EWindowId windowId)
{
    const int64_t l_setAvmViewidTime = chrono_ms();
    XLOG_ERROR(g_AppContext, "[svs]: l_setAvmViewidTime is " << static_cast<vfc::int32_t>(l_setAvmViewidTime)
                               );

    std::unique_lock<std::mutex> lock(mtx_linux);

    XLOG_ERROR(g_AppContext, "[svs]: setLayout is " << static_cast<vfc::int32_t>(viewId)
                               << "; windowID: " << static_cast<vfc::int32_t>(windowId) );

    static std::array<int, 5> s_windowViewId = { {-1, -1, -1, -1, -1} };
    s_windowViewId[static_cast<int>(windowId)] = viewId;

    // XLOG_ERROR(g_AppContext,"222222222222222[svs]: setLayout is: " << s_windowViewId[0] << ", "
    //                                                                 << s_windowViewId[1] << ", "
    //                                                                 << s_windowViewId[2] << ", "
    //                                                                 << s_windowViewId[3] << ", "
    //                                                                 << s_windowViewId[4] );
    int l_viewId = viewId;
    if (-1 == l_viewId)
    {
        for (int i = 0; i < s_windowViewId.size(); i++)
        {
            if (-1 != s_windowViewId[i])
            {
                l_viewId = s_windowViewId[i];
                break;
            }
        }
    }

    // XLOG_ERROR(g_AppContext,"333333333333333[svs]: l_viewId is " <<static_cast<vfc::int32_t>(l_viewId) <<
    // XLOG_ENDL;

    EScreenID l_screenId      = EScreenID_NO_CHANGE;
    cc::daddy::SideViewEnableStatus l_sideEnableSts = cc::daddy::SIDEVIEW_DISABLE;
    cc::daddy::TopViewEnableStatus  l_topEnableSts  = cc::daddy::TOPVIEW_DISABLE;

    if (l_viewId < 10000)
    {
        cc::util::common::mapViewIDFromSghl(l_viewId, l_screenId, l_sideEnableSts, l_topEnableSts);
    }
    else
    {
        cc::util::common::mapViewIDFromST2(l_viewId, l_screenId, l_sideEnableSts, l_topEnableSts);
    }

    if (true == cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.isConnected())
    {
        XLOG_ERROR(g_COMSocketContext, "DEBUG MEMPOOL setAvmView: sm_HUDislayModeSwitchDaddy_SenderPort.reserve() " << static_cast<int>(l_screenId));
        cc::daddy::HUDislayModeSwitchDaddy_t& l_EScreenID =
            cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.reserve();
        l_EScreenID.m_Data = l_screenId; // PRQA S 3013
        // XLOG_ERROR(g_AppContext,"[Inside svs]: setLayout is " <<static_cast<vfc::int32_t>(l_EScreenID.m_Data)<<
        // XLOG_ENDL;
        cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.deliver();
    }

    if (true == cc::daddy::CustomDaddyPorts::sm_sideViewEnableStatus_SenderPort.isConnected())
    {
        XLOG_ERROR(g_COMSocketContext, "DEBUG MEMPOOL setAvmView: sm_sideViewEnableStatus_SenderPort.reserve() " << static_cast<int>(l_sideEnableSts));
        cc::daddy::SideViewEnableStatusDaddy& l_data =
            cc::daddy::CustomDaddyPorts::sm_sideViewEnableStatus_SenderPort.reserve();
        l_data.m_Data = l_sideEnableSts; // PRQA S 3013
        // XLOG_ERROR(g_AppContext,"[Inside svs]: m_sideEnableSts is "
        // <<f_StrippedCpjVal_in.m_cpjHMI.m_sideEnableSts);
        cc::daddy::CustomDaddyPorts::sm_sideViewEnableStatus_SenderPort.deliver();
    }

    if (true == cc::daddy::CustomDaddyPorts::sm_topViewEnableStatus_SenderPort.isConnected())
    {
        XLOG_ERROR(g_COMSocketContext, "DEBUG MEMPOOL setAvmView: sm_topViewEnableStatus_SenderPort.reserve() " << static_cast<int>(l_topEnableSts));
        cc::daddy::TopViewEnableStatusDaddy& l_data =
            cc::daddy::CustomDaddyPorts::sm_topViewEnableStatus_SenderPort.reserve();
        l_data.m_Data = l_topEnableSts; // PRQA S 3013
        cc::daddy::CustomDaddyPorts::sm_topViewEnableStatus_SenderPort.deliver();
    }

    // g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_huDisplayID = static_cast<vfc::uint8_t>(l_screenId);
    // g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_sideEnableSts = static_cast<vfc::uint8_t>(l_sideEnableSts);
    // g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_topEnableSts = static_cast<vfc::uint8_t>(l_topEnableSts);

    m_screemIDRequestPre = m_screemIDRequestCur;
    if (l_screenId != 0 && l_screenId != -1)
    {
        m_screemIDRequestCur = l_screenId;
    }
    XLOG_ERROR(g_AppContext,"[setAvmView]: m_screemIDRequestPre: "<< m_screemIDRequestPre<<", m_screemIDRequestCur" <<m_screemIDRequestCur);

    return 0;
}

// Implement OnTouchEvent function
// x     Single finger touch x coordinate or center x coordinate of two-finger touch
// y     Single finger touch y coordinate or center y coordinate of two-finger touch
int com_linux_user::OnTouchEvent(AVMTouchEventType action, int x, int y, float panVelocity)
{
    XLOG_ERROR(g_AppContext, "[svs]:OnTouchEvent !!!!!! " );
    g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_touchCoorX      = x;
    g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_touchCoorY      = y;
    g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_touchSwipeSpeed = panVelocity;

    XLOG_ERROR(g_AppContext, "[svs]:touch x is " << g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_touchCoorX
                               );
    XLOG_ERROR(g_AppContext, "[svs]:touch y is " << g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_touchCoorY
                               );
    XLOG_ERROR(g_AppContext, "[svs]:touch speed is "
                               << g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_touchSwipeSpeed );

    switch (action)
    {
    case AVMTouchEventType::AVM_TOUCH_DOWN:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_touchEvenType = 1;
        break;
    }
    case AVMTouchEventType::AVM_TOUCH_UP:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_touchEvenType = 2;
        break;
    }
    case AVMTouchEventType::AVM_TOUCH_SINGLE_MOVE:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_touchEvenType = 3;
        break;
    }
    default:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_touchEvenType = 0;
        break;
    }
    }
    return 0;
}

int com_linux_user::onTwoFingersTouchEvent(AVMTouchEventType action, int x1, int y1, int x2, int y2)
{
    // std::unique_lock<std::mutex> lock(mtx_linux);
    // XLOG_ERROR(g_AppContext,"[svs]:onTwoFingersTouchEvent !!!!!! " );
    g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_touch1CoorX = x1;
    g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_touch1CoorY = y1;
    g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_touch2CoorX = x2;
    g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_touch2CoorY = y2;

    XLOG_ERROR(g_AppContext,"[svs]:finger 1: touch x is " <<g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_touch1CoorX ); 
    XLOG_ERROR(g_AppContext, "[svs]:finger 1: touch y is " << g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_touch1CoorY );
    XLOG_ERROR(g_AppContext, "[svs]:finger 2: touch x is " << g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_touch2CoorX ); 
    XLOG_ERROR(g_AppContext, "[svs]:finger 2: touch y is " << g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_touch2CoorY );
    static vfc::uint8_t s_twoFingerTouchEvenType = 0u;
    vfc::uint8_t        l_twoFingerTouchEvenType = 0u;
    switch (action)
    {
        case AVMTouchEventType::AVM_TOUCH_DOUBLE_START:
        {
            XLOG_ERROR(g_AppContext,"[svs]:two finger touch Start" );
            l_twoFingerTouchEvenType = 1u;
            break;
        }
        case AVMTouchEventType::AVM_TOUCH_DOUBLE_MOVE:
        {
            XLOG_ERROR(g_AppContext,"[svs]:two finger touch Move" );
            l_twoFingerTouchEvenType = 2u;
            break;
        }
        case AVMTouchEventType::AVM_TOUCH_DOUBLE_FINISH:
            XLOG_ERROR(g_AppContext,"[svs]:two finger touch Finish" );
        {
            l_twoFingerTouchEvenType = 3u;
            break;
        }
        default:
            XLOG_ERROR(g_AppContext, "[svs]:invalid two finger touch type: "<< static_cast<int>(action));
        {
            l_twoFingerTouchEvenType = 0u;
            break;
        }
    }
    if (l_twoFingerTouchEvenType != s_twoFingerTouchEvenType)
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_twoFingerTouchEvenType = l_twoFingerTouchEvenType;
    }
    s_twoFingerTouchEvenType = l_twoFingerTouchEvenType;
    return 0;
}

// Implement setSlideCallback function
// typedef void (*SlideCallback)(void *data, int size);
// data type is int, and size is not used.
// enum AVMAnimationCallback
// {
//     AVM_ANI_SLIDE_LEFT = 0,         // Slide to reset left view
//     AVM_ANI_SLIDE_RIGHT,            // Slide to reset right view
//     AVM_ANI_DEFAULT_FULL_START,     // Default bird's eye view to full screen bird's eye view start
//     AVM_ANI_DEFAULT_FULL_STOP,      // Default bird's eye view to full screen bird's eye view end
//     AVM_ANI_FULL_DEFAULT_START,     // Full screen bird's eye view to default bird's eye view start
//     AVM_ANI_FULL_DEFAULT_STOP,      // Full screen bird's eye view to default bird's eye view end
//     AVM_ANI_SLIDE_FRONT_HUBVIEW,    // Slide to reset front hub view
//     AVM_ANI_SLIDE_REAR_HUBVIEW,     // Slide to reset rear hub view
// };
int com_linux_user::setSlideCallback(SlideCallback callback)
{
    if (callback != nullptr)
    {
        g_dataContainerFromSvs.m_slideCallback = callback;
    }
    return 0;
}

// Implement stopCalibration function
int com_linux_user::stopCalibration()
{
    // TODO: Implement this function
    return -1;
}

// void (*CalibrationCallback)(void *data, int size) // Parameters void *data, int size are not used, callback indicates calibration success
int com_linux_user::startCalibration(CalibrationMode mode, CalibrationCallback callback)
{
    XLOG_ERROR(g_AppContext, "[svs]:m_startCalibration !!!!!!!!!!!!!!!!!!!!!!!!! " );
    XLOG_ERROR(g_AppContext, "[svs]: m_startCalibration mode is !!" << static_cast<int>(mode) );
    if (CalibrationMode::AUTOMATIC_CALIBRATION == mode)
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_startCalibration = true;
        XLOG_ERROR(g_AppContext, "[svs]:m_startCalibration is true " );
    }

    if (true == cc::daddy::CustomDaddyPorts::sm_HUCalibrationStartDaddy_SenderPort.isConnected())
    {
        XLOG_ERROR(g_COMSocketContext, "DEBUG MEMPOOL startCalibration: sm_HUCalibrationStartDaddy_SenderPort.reserve() " << static_cast<int>(mode));
        auto& l_rData  = cc::daddy::CustomDaddyPorts::sm_HUCalibrationStartDaddy_SenderPort.reserve();
        l_rData.m_Data = g_dataContainerToSvs.m_StrippedCpjValData.m_startCalibration;
        XLOG_ERROR(g_AppContext, "[svs]: m_startCalibration daddy is !!" << static_cast<int>(l_rData.m_Data)
                                   );
        cc::daddy::CustomDaddyPorts::sm_HUCalibrationStartDaddy_SenderPort.deliver();
        // g_dataContainerToSvs.m_StrippedCpjValData.m_intrinsicUpdated = false;
    }

    if (callback != nullptr)
    {
        g_dataContainerFromSvs.m_calibCallback = callback;
    }
    else
    {
        XLOG_ERROR(g_AppContext, "[svs]: cpc callback is empty!!!!!" );
    }

    return 0;
}

// Implement setCarSpeed function km/h
int com_linux_user::setCarSpeed(float speed)
{
    float l_vehSpeed = speed * 0.27778f; // km/h to m/s   1/3.6=0.2777778
    g_dataContainerToSvs.m_StrippedPfValData.m_vehicleVelocity =
        static_cast<vfc::CSI::si_metre_per_second_f32_t>(l_vehSpeed);
    // XLOG_ERROR_OS(g_AppContext)
    //     << "[!!!!!!!!!!!!!!!!!]: g_dataContainerToSvs.m_StrippedPfValData.m_vehicleVelocity is !!"
    //     << g_dataContainerToSvs.m_StrippedPfValData.m_vehicleVelocity.value() );

    // set wheel direction for vhm
    if (l_vehSpeed < 0.001f) // m/s
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFL = valin::EWheelDrvDir::WHEEL_DRV_DIR_STOP;
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFR = valin::EWheelDrvDir::WHEEL_DRV_DIR_STOP;
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRR = valin::EWheelDrvDir::WHEEL_DRV_DIR_STOP;
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRL = valin::EWheelDrvDir::WHEEL_DRV_DIR_STOP;
        g_dataContainerToSvs.m_StrippedPfValData.m_vehicleDrvDir = cc::target::common::EVehicleDrvDir::DRVDIR_VOID;
    }
    else
    {
        //   GEAR_PARK = 0, GEAR_REVERSE = 1,   GEAR_NEUTRAL = 2,  GEAR_DRIVE = 3,  GEAR_NOSIGNAL = 4,
        switch (g_dataContainerToSvs.m_StrippedPfValData.m_gearStatus)
        {
        case cc::target::common::EGearStatus::GEAR_REVERSE: // R gear
        {
            g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFL =
                valin::EWheelDrvDir::WHEEL_DRV_DIR_BWD;
            g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFR =
                valin::EWheelDrvDir::WHEEL_DRV_DIR_BWD;
            g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRR =
                valin::EWheelDrvDir::WHEEL_DRV_DIR_BWD;
            g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRL =
                valin::EWheelDrvDir::WHEEL_DRV_DIR_BWD;
            g_dataContainerToSvs.m_StrippedPfValData.m_vehicleDrvDir = cc::target::common::EVehicleDrvDir::DRVDIR_BWD;
            break;
        }
        case cc::target::common::EGearStatus::GEAR_DRIVE: // D gear
        {
            g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFL =
                valin::EWheelDrvDir::WHEEL_DRV_DIR_FWD;
            g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFR =
                valin::EWheelDrvDir::WHEEL_DRV_DIR_FWD;
            g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRR =
                valin::EWheelDrvDir::WHEEL_DRV_DIR_FWD;
            g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRL =
                valin::EWheelDrvDir::WHEEL_DRV_DIR_FWD;
            g_dataContainerToSvs.m_StrippedPfValData.m_vehicleDrvDir = cc::target::common::EVehicleDrvDir::DRVDIR_FWD;
            break;
        }
        default:
        {
            g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFL =
                valin::EWheelDrvDir::WHEEL_DRV_DIR_UNKNOWN;
            g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFR =
                valin::EWheelDrvDir::WHEEL_DRV_DIR_UNKNOWN;
            g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRR =
                valin::EWheelDrvDir::WHEEL_DRV_DIR_UNKNOWN;
            g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRL =
                valin::EWheelDrvDir::WHEEL_DRV_DIR_UNKNOWN;
            g_dataContainerToSvs.m_StrippedPfValData.m_vehicleDrvDir = cc::target::common::EVehicleDrvDir::DRVDIR_VOID;
            break;
        }
        }
    }
        // XLOG_INFO(g_AppContext, "[svs]: g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFL is !!" <<static_cast<int>(g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFL )); 
        // XLOG_INFO(g_AppContext, "[svs]: g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFR is !!" <<static_cast<int>(g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFR )); 
        // XLOG_INFO(g_AppContext, "[svs]: g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRR is !!" <<static_cast<int>(g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRR )); 
        // XLOG_INFO(g_AppContext, "[svs]: g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRL is !!" <<static_cast<int>(g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRL ));
    // XLOG_ERROR_OS(g_AppContext)
    //     << "[!!!!!!!!!!!!!!!!!]: g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFL is !!"
    //     << static_cast<int>(g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFL) );

    // set wheel direction for vhm
    const vfc::float32_t l_rpmFloat =
    l_vehSpeed * 60.0f / (2.0f * 3.1415926f * pc::vehicle::g_mechanicalData->m_wheelRadius);
    vfc::uint16_t l_rpm = static_cast<vfc::uint16_t>(l_rpmFloat);
    // vfc::uint16_t l_rpm = static_cast<vfc::uint16_t>(
    //     l_vehSpeed * 60.0f / (2.0f * 3.1415926f * pc::vehicle::g_mechanicalData->m_wheelRadius));
    float l_ratio = std::tan(std::abs(g_dataContainerToSvs.m_StrippedPfValData.m_frontWheelAngle.value()));
    // XLOG_ERROR(g_AppContext, "[***************!!!!!!!!!!!!!!!!!]: l_ratio is !!" << l_ratio );
    float l_r = pc::vehicle::g_mechanicalData->m_wheelbase / l_ratio;
    // XLOG_ERROR(g_AppContext, "[!!!!!!!!!!!!!!!!!]: l_r is !!" << l_r );
    float l_diffRatio = (l_r + pc::vehicle::g_mechanicalData->m_trackRear) / l_r;
    // XLOG_ERROR(g_AppContext, "[!!!!!!!!!!!!!!!!!]: l_diffRatio is !!" << l_diffRatio );
    if (g_dataContainerToSvs.m_StrippedPfValData.m_driverSteeringWheelAngle.value() > 0)
    {
        // Left turn
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationFL = l_rpm;
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationRL = l_rpm;
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationFR = l_rpm * l_diffRatio;
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationRR = l_rpm * l_diffRatio;
    }
    else
    {
        // Right turn
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationFL = l_rpm * l_diffRatio;
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationRL = l_rpm * l_diffRatio;
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationFR = l_rpm;
        g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationRR = l_rpm;
    }
    g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationFLQualifier =
        valin::EWheelRotationQualifier::Q_WHEEL_ROTATION_VALID;
    g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationFRQualifier =
        valin::EWheelRotationQualifier::Q_WHEEL_ROTATION_VALID;
    g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationRLQualifier =
        valin::EWheelRotationQualifier::Q_WHEEL_ROTATION_VALID;
    g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationRRQualifier =
        valin::EWheelRotationQualifier::Q_WHEEL_ROTATION_VALID;
    // XLOG_ERROR(g_AppContext, "[!!!!!!!!!!!!!!!!!]: g_dataContainerToSvs.l_rpm left is !!"
                            //    << g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationFL );
    // XLOG_ERROR(g_AppContext, "[!!!!!!!!!!!!!!!!!]: g_dataContainerToSvs.l_rpm right is !!"
                            //    << g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationFR );
        // XLOG_INFO(g_AppContext, "[svs]: g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationFL is !!" <<g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationFL ); 
        // XLOG_INFO(g_AppContext, "[svs]: g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationRL is !!" <<g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationRL ); 
        // XLOG_INFO(g_AppContext, "[svs]: g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationFR is !!" <<g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationFR ); 
        // XLOG_INFO(g_AppContext, "[svs]: g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationRR is !!" <<g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelRotationRR );
    return 0;
}

// Implement SetVehicleWheelPulse function
int com_linux_user::SetVehicleWheelPulse(unsigned char* wheelSpeedPulse, int /*size*/)
{
    // static int16_t s_preWheelImpCtrFL = 0;
    // static int16_t s_preWheelImpCtrFR = 0;
    // static int16_t s_preWheelImpCtrRL = 0;
    // static int16_t s_preWheelImpCtrRR = 0;
    static int s_logCtr = 0;
    g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelImpCtrFL =
        (static_cast<vfc::uint16_t>(wheelSpeedPulse[0]) << 8) + static_cast<vfc::uint16_t>(wheelSpeedPulse[1]);
    g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelImpCtrFR =
        (static_cast<vfc::uint16_t>(wheelSpeedPulse[2]) << 8) + static_cast<vfc::uint16_t>(wheelSpeedPulse[3]);
    g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelImpCtrRL =
        (static_cast<vfc::uint16_t>(wheelSpeedPulse[4]) << 8) + static_cast<vfc::uint16_t>(wheelSpeedPulse[5]);
    g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelImpCtrRR =
        (static_cast<vfc::uint16_t>(wheelSpeedPulse[6]) << 8) + static_cast<vfc::uint16_t>(wheelSpeedPulse[7]);
    // TODO 50次打一次
    // vfc::uint32_t l_timeStamp = (static_cast<vfc::uint32_t>(wheelSpeedPulse[12]) << 24) +
    //                             (static_cast<vfc::uint32_t>(wheelSpeedPulse[13]) << 16) +
    //                             (static_cast<vfc::uint32_t>(wheelSpeedPulse[14]) << 8) +
    //                             (static_cast<vfc::uint32_t>(wheelSpeedPulse[15]));

    vfc::uint32_t l_timeStamp_ =
        (((0xff & wheelSpeedPulse[12])) | ((0xff & wheelSpeedPulse[13] << 8)) |
         ((0xff & wheelSpeedPulse[14]) << 16) | ((0xff & wheelSpeedPulse[15] << 24)));

    g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelImpCtrFLTimestamp = tic::CGlobalTimestamp(l_timeStamp_);
    g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelImpCtrFRTimestamp = tic::CGlobalTimestamp(l_timeStamp_);
    g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelImpCtrRLTimestamp = tic::CGlobalTimestamp(l_timeStamp_);
    g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelImpCtrRRTimestamp = tic::CGlobalTimestamp(l_timeStamp_);
    
    if (s_logCtr % 100 == 0)
    {
        XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.m_PasAPPData.m_wheelImpCtrFL is !!" <<g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelImpCtrFL ); 
        XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.m_PasAPPData.m_wheelImpCtrFR is !!" <<g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelImpCtrFR ); 
        XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.m_PasAPPData.m_wheelImpCtrRL is !!" <<g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelImpCtrRL ); 
        XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.m_PasAPPData.m_wheelImpCtrRR is !!" <<g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelImpCtrRR );
        // XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.m_PasAPPData.l_timeStamp is !!" << l_timeStamp); 
        XLOG_ERROR(g_AppContext,"[svs]: g_dataContainerToSvs.m_PasAPPData.l_timeStamp__ is !!" << l_timeStamp_ );
    }
    s_logCtr ++;

    return 0;
}

// Implement setFrontRearRadarDistance function
int com_linux_user::setFrontRearRadarDistance(int* distance, int /*length*/)
{
    g_dataContainerToSvs.m_LSMGData.m_pasStatus = cc::target::common::PAS_FrontRearActive;
#if USE_RADAR_WALL
    g_dataContainerToSvs.m_PasAPPData.m_front.m_dist2VehHighObj[0]  = distance[0];
    g_dataContainerToSvs.m_PasAPPData.m_front.m_dist2VehHighObj[1]  = distance[0];
    g_dataContainerToSvs.m_PasAPPData.m_front.m_dist2VehHighObj[2]  = distance[0];
    g_dataContainerToSvs.m_PasAPPData.m_front.m_dist2VehHighObj[3]  = distance[6];
    g_dataContainerToSvs.m_PasAPPData.m_front.m_dist2VehHighObj[4]  = distance[6];
    g_dataContainerToSvs.m_PasAPPData.m_front.m_dist2VehHighObj[5]  = distance[6];
    g_dataContainerToSvs.m_PasAPPData.m_front.m_dist2VehHighObj[6]  = distance[7];
    g_dataContainerToSvs.m_PasAPPData.m_front.m_dist2VehHighObj[7]  = distance[7];
    g_dataContainerToSvs.m_PasAPPData.m_front.m_dist2VehHighObj[8]  = distance[7];
    g_dataContainerToSvs.m_PasAPPData.m_front.m_dist2VehHighObj[9]  = distance[1];
    g_dataContainerToSvs.m_PasAPPData.m_front.m_dist2VehHighObj[10] = distance[1];
    g_dataContainerToSvs.m_PasAPPData.m_front.m_dist2VehHighObj[11] = distance[1];

    g_dataContainerToSvs.m_PasAPPData.m_rear.m_dist2VehHighObj[0]  = distance[3];
    g_dataContainerToSvs.m_PasAPPData.m_rear.m_dist2VehHighObj[1]  = distance[3];
    g_dataContainerToSvs.m_PasAPPData.m_rear.m_dist2VehHighObj[2]  = distance[3];
    g_dataContainerToSvs.m_PasAPPData.m_rear.m_dist2VehHighObj[3]  = distance[5];
    g_dataContainerToSvs.m_PasAPPData.m_rear.m_dist2VehHighObj[4]  = distance[5];
    g_dataContainerToSvs.m_PasAPPData.m_rear.m_dist2VehHighObj[5]  = distance[5];
    g_dataContainerToSvs.m_PasAPPData.m_rear.m_dist2VehHighObj[6]  = distance[4];
    g_dataContainerToSvs.m_PasAPPData.m_rear.m_dist2VehHighObj[7]  = distance[4];
    g_dataContainerToSvs.m_PasAPPData.m_rear.m_dist2VehHighObj[8]  = distance[4];
    g_dataContainerToSvs.m_PasAPPData.m_rear.m_dist2VehHighObj[9]  = distance[2];
    g_dataContainerToSvs.m_PasAPPData.m_rear.m_dist2VehHighObj[10] = distance[2];
    g_dataContainerToSvs.m_PasAPPData.m_rear.m_dist2VehHighObj[11] = distance[2];

    // g_dataContainerToSvs.m_PasAPPData.m_left.m_dist2VehHighObj[0] = distance[2];
    // g_dataContainerToSvs.m_PasAPPData.m_left.m_dist2VehHighObj[7] = distance[0];

    // g_dataContainerToSvs.m_PasAPPData.m_right.m_dist2VehHighObj[0] = distance[1];
    // g_dataContainerToSvs.m_PasAPPData.m_right.m_dist2VehHighObj[7] = distance[3];
#else
    // g_dataContainerToSvs.m_PasAPPData.PasAPPDistanceAllObjDataFront[0] = distance[0]; //From left to right
    // g_dataContainerToSvs.m_PasAPPData.PasAPPDistanceAllObjDataFront[1] = distance[6];
    // g_dataContainerToSvs.m_PasAPPData.PasAPPDistanceAllObjDataFront[2] = distance[7];
    // g_dataContainerToSvs.m_PasAPPData.PasAPPDistanceAllObjDataFront[3] = distance[1];

    // g_dataContainerToSvs.m_PasAPPData.PasAPPDistanceAllObjDataRear[0] = distance[3];
    // g_dataContainerToSvs.m_PasAPPData.PasAPPDistanceAllObjDataRear[1] = distance[5];
    // g_dataContainerToSvs.m_PasAPPData.PasAPPDistanceAllObjDataRear[2] = distance[4];
    // g_dataContainerToSvs.m_PasAPPData.PasAPPDistanceAllObjDataRear[3] = distance[2];

    g_dataContainerToSvs.m_LSMGData.LsmgDistanceDataFront[0] = distance[0];
    g_dataContainerToSvs.m_LSMGData.LsmgDistanceDataFront[1] = distance[6];
    g_dataContainerToSvs.m_LSMGData.LsmgDistanceDataFront[2] = distance[7];
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.m_PasAPPData.LsmgDistanceDataFront[2] is !!"
                               << distance[7] );
    g_dataContainerToSvs.m_LSMGData.LsmgDistanceDataFront[3] = distance[1];

    g_dataContainerToSvs.m_LSMGData.LsmgDistanceDataRear[0] = distance[3];
    g_dataContainerToSvs.m_LSMGData.LsmgDistanceDataRear[1] = distance[5];
    g_dataContainerToSvs.m_LSMGData.LsmgDistanceDataRear[2] = distance[4];
    g_dataContainerToSvs.m_LSMGData.LsmgDistanceDataRear[3] = distance[2];
#endif
    // XLOG_ERROR(g_AppContext,"[svs]: g_dataContainerToSvs.m_PasAPPData.LsmgDistanceDataFront[2] is !!" <<
    // distance[7] );

    return 0;
}

// Implement setLeftRightRadarDistance function
int com_linux_user::setLeftRightRadarDistance(int* distance, int /*length*/)
{
#if USE_RADAR_WALL
    g_dataContainerToSvs.m_PasAPPData.m_left.m_dist2VehHighObj[0] = distance[6];
    g_dataContainerToSvs.m_PasAPPData.m_left.m_dist2VehHighObj[1] = distance[6];
    g_dataContainerToSvs.m_PasAPPData.m_left.m_dist2VehHighObj[2] = distance[4];
    g_dataContainerToSvs.m_PasAPPData.m_left.m_dist2VehHighObj[3] = distance[4];
    g_dataContainerToSvs.m_PasAPPData.m_left.m_dist2VehHighObj[4] = distance[2];
    g_dataContainerToSvs.m_PasAPPData.m_left.m_dist2VehHighObj[5] = distance[2];
    g_dataContainerToSvs.m_PasAPPData.m_left.m_dist2VehHighObj[6] = distance[0];
    g_dataContainerToSvs.m_PasAPPData.m_left.m_dist2VehHighObj[7] = distance[0];
    // g_dataContainerToSvs.m_PasAPPData.m_left.m_dist2VehHighObj[8] = distance[2];
    // g_dataContainerToSvs.m_PasAPPData.m_left.m_dist2VehHighObj[9]  = distance[0];
    // g_dataContainerToSvs.m_PasAPPData.m_left.m_dist2VehHighObj[10] = distance[0];
    // g_dataContainerToSvs.m_PasAPPData.m_left.m_dist2VehHighObj[11] = distance[0];

    g_dataContainerToSvs.m_PasAPPData.m_right.m_dist2VehHighObj[0] = distance[1];
    g_dataContainerToSvs.m_PasAPPData.m_right.m_dist2VehHighObj[1] = distance[1];
    g_dataContainerToSvs.m_PasAPPData.m_right.m_dist2VehHighObj[2] = distance[3];
    g_dataContainerToSvs.m_PasAPPData.m_right.m_dist2VehHighObj[3] = distance[3];
    g_dataContainerToSvs.m_PasAPPData.m_right.m_dist2VehHighObj[4] = distance[5];
    g_dataContainerToSvs.m_PasAPPData.m_right.m_dist2VehHighObj[5] = distance[5];
    g_dataContainerToSvs.m_PasAPPData.m_right.m_dist2VehHighObj[6] = distance[7];
    g_dataContainerToSvs.m_PasAPPData.m_right.m_dist2VehHighObj[7] = distance[7];
    // g_dataContainerToSvs.m_PasAPPData.m_right.m_dist2VehHighObj[8] = distance[5];
    // g_dataContainerToSvs.m_PasAPPData.m_right.m_dist2VehHighObj[9] = distance[7];
    // g_dataContainerToSvs.m_PasAPPData.m_right.m_dist2VehHighObj[10] = distance[7];
    // g_dataContainerToSvs.m_PasAPPData.m_right.m_dist2VehHighObj[11] = distance[7];

    // XLOG_ERROR(g_AppContext,"[svs]: g_dataContainerToSvs.m_PasAPPData.m_right.m_dist2VehHighObj[7] is !!" <<
    // g_dataContainerToSvs.m_PasAPPData.m_right.m_dist2VehHighObj[7] );

    // for(int index=0; index<length;index++)
    // {
    //     XLOG_ERROR(g_AppContext,"[svs]: setLeftRightRadarDistance daddy is !!" << static_cast<int>
    //     (distance[index]) );
    // }

#else
    // g_dataContainerToSvs.m_PasAPPData.PasAPPDistanceAllObjDataLeft[0] = distance[3];
    // g_dataContainerToSvs.m_PasAPPData.PasAPPDistanceAllObjDataLeft[1] = distance[2];
    // g_dataContainerToSvs.m_PasAPPData.PasAPPDistanceAllObjDataLeft[2] = distance[1];
    // g_dataContainerToSvs.m_PasAPPData.PasAPPDistanceAllObjDataLeft[3] = distance[0];

    // g_dataContainerToSvs.m_PasAPPData.PasAPPDistanceAllObjDataLeft[0] = distance[4];
    // g_dataContainerToSvs.m_PasAPPData.PasAPPDistanceAllObjDataLeft[1] = distance[5];
    // g_dataContainerToSvs.m_PasAPPData.PasAPPDistanceAllObjDataLeft[2] = distance[6];
    // g_dataContainerToSvs.m_PasAPPData.PasAPPDistanceAllObjDataLeft[3] = distance[7];
    g_dataContainerToSvs.m_LSMGData.LsmgDistanceDataLeft[0] = distance[6];
    g_dataContainerToSvs.m_LSMGData.LsmgDistanceDataLeft[1] = distance[4];
    g_dataContainerToSvs.m_LSMGData.LsmgDistanceDataLeft[2] = distance[2];
    g_dataContainerToSvs.m_LSMGData.LsmgDistanceDataLeft[3] = distance[0];

    g_dataContainerToSvs.m_LSMGData.LsmgDistanceDataRight[0] = distance[1];
    g_dataContainerToSvs.m_LSMGData.LsmgDistanceDataRight[1] = distance[3];
    g_dataContainerToSvs.m_LSMGData.LsmgDistanceDataRight[2] = distance[5];
    g_dataContainerToSvs.m_LSMGData.LsmgDistanceDataRight[3] = distance[7];
#endif

    return 0;
}

// Implement setGuidelineDisplay function
void com_linux_user::setGuidelineDisplay(CarGuideLine /*mode*/)
{
    // TODO: Implement this function
    return;
}

// Implement setVehicleSteerAngleArray function
// steerAngle: steering wheel angle
// length = 6, last four bits are timestamp
// 0~1 steering wheel angle*10
// Example: 0E10 corresponds to number (last four bits are timestamp) [14,16,1,42,60,22] = 3600 * 0.1 = 360.0
// Timestamp value ((0x00ff & angle[2] << 24) | ((0x00ff & angle[3] << 16) | ((0x00ff & angle[4]) << 8) | (0x00ff &
// angle[5])): 012A3C16
void com_linux_user::setVehicleSteerAngleArray(char* steerAngle, int /*length*/)
{
    const vfc::uint16_t rawValue = static_cast<vfc::uint16_t>(((steerAngle[0] & 255) << 8) | (steerAngle[1] & 255));
    vfc::int16_t angle = static_cast<vfc::int16_t>(rawValue);
    // vfc::int16_t angle = static_cast<vfc::int16_t>(((steerAngle[0] & 255) << 8) + (steerAngle[1] & 255));

    vfc::float32_t l_angles = static_cast<vfc::float32_t>(angle) * 0.1f;

    // XLOG_ERROR(g_AppContext, "[svs]: setVehicleSteerAngleArray is !!!!!!!!!!!!!!!!!!!" << l_angles );

    bool l_dirty{false};
    // XLOG_ERROR(g_COMSocketContext, "DEBUG MEMPOOL setVehicleSteerAngleArray: l_angles = " << l_angles);
    if (g_dataContainerToSvs.m_StrippedPfValData.m_driverSteeringWheelAngle !=
        static_cast<vfc::CSI::si_degree_f32_t>(l_angles))
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_driverSteeringWheelAngle =
            static_cast<vfc::CSI::si_degree_f32_t>(l_angles);
        l_dirty = true;
    }

    if (true == cc::daddy::CustomDaddyPorts::sm_DriverSteeringWheelAngleDaddy_SenderPort.isConnected() && l_dirty)
    {
        // XLOG_ERROR(g_COMSocketContext, "DEBUG MEMPOOL setVehicleSteerAngleArray: sm_DriverSteeringWheelAngleDaddy_SenderPort.reserve() " << l_angles);
        auto& l_rData = cc::daddy::CustomDaddyPorts::sm_DriverSteeringWheelAngleDaddy_SenderPort.reserve();

        l_rData.m_Data =
            static_cast<float>(g_dataContainerToSvs.m_StrippedPfValData.m_driverSteeringWheelAngle.value());
        cc::daddy::CustomDaddyPorts::sm_DriverSteeringWheelAngleDaddy_SenderPort.deliver();
    }
}

/************************************************************************
 * func   : Set the rear wheel rotation angle
 * input  : valid    valid validity flag; 0, invalid; 1. valid
 *          left     Left rear wheel angle; [-200, 200], unit: 0.1 degrees; It means the physical value ranges from -20 degrees to 20 degrees
 *          right    Right rear wheel angle; [-200, 200], unit: 0.1 degrees; It means the physical value ranges from -20 degrees to 20 degrees
 * return : 0, Success
 *          Not 0, Failed
 ************************************************************************/
int com_linux_user::setRearWheelAngle(bool valid, int left, int right)
{
    if (valid)
    {
        vfc::CSI::si_degree_f32_t f_val = static_cast<vfc::CSI::si_degree_f32_t>(static_cast<vfc::float32_t>(left + right) * 0.05f);
        g_dataContainerToSvs.m_StrippedPfValData.m_rearWheelAngle = f_val;
    }

    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.m_StrippedPfValData.m_rearWheelAngleis !!"
                               << g_dataContainerToSvs.m_StrippedPfValData.m_rearWheelAngle.value() );
    return 0;
}

// Implement setCarTransparent function
// transparent:
// On  -> Trun on transparent mode
// 0ff -> Turn off transparent mode
void com_linux_user::setCarTransparent(bool transparent)
{
    g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehTransReq = static_cast<vfc::uint8_t>(transparent);

    if (transparent == false)
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehTransLevel = static_cast<vfc::uint8_t>(cc::target::common::ETransparentMode::INVAILD);
        m_tranparentLevel = g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehTransLevel;
    }

    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehTransReq is !!"
                               << static_cast<int>(g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehTransReq)
                               );
}

// Implement setCarTransparentMode function
// mode 0: not_transparent; 1: Half_transparent; 2: Total_transparent; default is 1
// enum AVMTransparentMode
// {
//     AVM_FULLY_TRANSPARENT,    // 3D crystal transparent, 2D fully transparent (default value, bosch special handling)
//     AVM_SIMPLE,               // 3D minimal, 2D fully transparent, not processed
//     AVM_NOT_TRANSPARENT,      // Not transparent
// };
// BYD does not send AVM_SIMPLE
// Bosch special handling AVM_FULLY_TRANSPARENT - 3D crystal transparent, 2D fully transparent
void com_linux_user::setCarTransparentMode(AVMTransparentMode mode)
{
    switch (mode)
    {
    case AVMTransparentMode::AVM_FULLY_TRANSPARENT:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehTransLevel = static_cast<vfc::uint8_t>(cc::target::common::ETransparentMode::CRYSTAL_TRANSPARENT);
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehTransReq   = 1u;
        break;
    }
    // case AVMTransparentMode::AVM_SIMPLE:
    //     g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehTransLevel =
    //     cc::target::common::COMPLETE_TRANSPARENT; g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehTransReq =
    //     1u; break;
    default:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehTransLevel = static_cast<vfc::uint8_t>(cc::target::common::ETransparentMode::INVAILD);
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehTransReq   = 1u;
        break;
    }
    }
    m_tranparentLevel = g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehTransLevel;
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehTransLevel  is !!"
                               << static_cast<int>(g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_vehTransLevel)
                               );
}

// Implement setLightStatus function
int com_linux_user::setLightStatus(AVMLightType light, AVMState state)
{
    XLOG_ERROR(g_AppContext, "[svs]: CarLightId is !!" << static_cast<int>(light) );
    XLOG_ERROR(g_AppContext, "[svs]: CarLightStatus is !!" << static_cast<int>(state) );
    switch (light)
    {
    case AVMLightType::AVM_LIGHT_LEFT:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjEnvVeh.m_leftIndicatorBlinkState =
            static_cast<vfc::uint8_t>(state);
        break;
    }
    case AVMLightType::AVM_LIGHT_RIGHT:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjEnvVeh.m_rightIndicatorBlinkState =
            static_cast<vfc::uint8_t>(state);
        break;
    }
    case AVMLightType::AVM_LIGHT_DOUBLE:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjEnvVeh.m_hazardLightState = static_cast<vfc::uint8_t>(state);
        break;
    }
    case AVMLightType::AVM_LIGHT_FAR:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjEnvVeh.m_mainBeamIndication = updateLightBool(state);
        break;
    }
    case AVMLightType::AVM_LIGHT_NEAR:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjEnvVeh.m_lowBeamIndication = updateLightBool(state);
        break;
    }
    case AVMLightType::AVM_LIGHT_BRAKE:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjPwrTm.m_brakeLampOnStatus = static_cast<vfc::uint8_t>(state);
        break;
    }
    case AVMLightType::AVM_LIGHT_REAR_POSITION:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjEnvVeh.m_rearPosLightState = static_cast<vfc::uint8_t>(state);
        break;
    }
    case AVMLightType::AVM_LIGHT_FRONT_POSITION:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjEnvVeh.m_frontPosLightState = static_cast<vfc::uint8_t>(state);
        break;
    }
    case AVMLightType::AVM_LIGHT_DAYTIME:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjEnvVeh.m_leftHeadLightState  = updateLightBool(state);
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjEnvVeh.m_rightHeadLightState = updateLightBool(state);
        break;
    }
    case AVMLightType::AVM_LIGHT_DAYTIME_LEFT:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjEnvVeh.m_leftHeadLightState = updateLightBool(state);
        break;
    }
    case AVMLightType::AVM_LIGHT_DAYTIME_RIGHT:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjEnvVeh.m_rightHeadLightState = updateLightBool(state);
        break;
    }
    case AVMLightType::AVM_LIGHT_REAR_FOG:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjEnvVeh.m_fogLightStatus = static_cast<uint8_t>(updateLightBool(state));
        break;
    }
    case AVMLightType::AVM_LIGHT_PROFILE:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjEnvVeh.m_frontClearanceLightState =
            static_cast<vfc::uint8_t>(state);
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjEnvVeh.m_rearClearanceLightState =
            static_cast<vfc::uint8_t>(state);
        break;
    }
    case AVMLightType::AVM_LIGHT_FRONT_PROFILE:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjEnvVeh.m_frontClearanceLightState =
            static_cast<vfc::uint8_t>(state);
        break;
    }
    case AVMLightType::AVM_LIGHT_BACK_PROFILE:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjEnvVeh.m_rearClearanceLightState =
            static_cast<vfc::uint8_t>(state);
        break;
    }
    case AVMLightType::AVM_LIGHT_LEFT_CORNER:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjEnvVeh.m_frontLeftCornerLightState =
            static_cast<vfc::uint8_t>(state);
        break;
    }
    case AVMLightType::AVM_LIGHT_RIGHT_CORNER:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjEnvVeh.m_frontRightCornerLightState =
            static_cast<vfc::uint8_t>(state);
        break;
    }
    case AVMLightType::AVM_LIGHT_REVERSE:
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjEnvVeh.m_reverseLightState = static_cast<vfc::uint8_t>(state);
        break;
    }
    default:
    {
        break;
    }
    }

    return 0;
}

// Implement setCarGear function
void com_linux_user::setCarGear(CarGearStatus gears)
{
    // EGearStatus
    // GEAR_PARK     = 0,
    // GEAR_REVERSE  = 1,
    // GEAR_NEUTRAL  = 2,
    // GEAR_DRIVE    = 3,
    // GEAR_NOSIGNAL = 4,
    // E_ALR_GEAR__P = 1, E_ALR_GEAR__R, E_ALR_GEAR__N, E_ALR_GEAR__D,

    int l_gear = static_cast<vfc::uint16_t>(gears);

    static cc::target::common::EGearStatus l_gearTemp = cc::target::common::EGearStatus::GEAR_NOSIGNAL;

    switch (l_gear)
    {
    case 1: // P
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_gearStatus = cc::target::common::EGearStatus::GEAR_PARK;
        break;
    }
    case 2: // R
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_gearStatus = cc::target::common::EGearStatus::GEAR_REVERSE;
        break;
    }
    case 3: // N
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_gearStatus = cc::target::common::EGearStatus::GEAR_NEUTRAL;
        break;
    }
    case 4: // D
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_gearStatus = cc::target::common::EGearStatus::GEAR_DRIVE;
        break;
    }
    default:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_gearStatus = cc::target::common::EGearStatus::GEAR_NOSIGNAL;
        break;
    }
    }

    l_gearTemp = g_dataContainerToSvs.m_StrippedPfValData.m_gearStatus;

    if ( pc::daddy::BaseDaddyPorts::sm_gearSenderPort.isConnected() )
    {
      XLOG_ERROR(g_COMSocketContext, "g_dataContainerToSvs.m_StrippedPfValData.m_gearStatus " << static_cast<int>(l_gearTemp));
      pc::daddy::GearDaddy& l_container = pc::daddy::BaseDaddyPorts::sm_gearSenderPort.reserve();
      pc::daddy::EGear l_convertedData = pc::daddy::GEAR_INIT ;

      // EGearStatus
      // GEAR_PARK     = 0,
      // GEAR_REVERSE  = 1,
      // GEAR_NEUTRAL  = 2,
      // GEAR_DRIVE    = 3,
      // GEAR_NOSIGNAL = 4,

      switch(g_dataContainerToSvs.m_StrippedPfValData.m_gearStatus)
      {
        case cc::target::common::EGearStatus::GEAR_PARK :
        {
          l_convertedData = pc::daddy::GEAR_P;
        } break;
        case cc::target::common::EGearStatus::GEAR_REVERSE :
        {
          l_convertedData = pc::daddy::GEAR_R;
        } break;
        case cc::target::common::EGearStatus::GEAR_NEUTRAL :
        {
          l_convertedData = pc::daddy::GEAR_N;
        } break;
        case cc::target::common::EGearStatus::GEAR_DRIVE :
        {
          l_convertedData = pc::daddy::GEAR_D;
        } break;
        case cc::target::common::EGearStatus::GEAR_NOSIGNAL :
        {
          l_convertedData = pc::daddy::GEAR_INIT;
        } break;
        default:
        {
          //unknown value
          l_convertedData = pc::daddy::GEAR_INVALID;
        } break;
      }

      l_container.m_Data = l_convertedData;
      pc::daddy::BaseDaddyPorts::sm_gearSenderPort.deliver();
    }
}

/************************************************************************
* func   :setCarRotateAngle
* input  :hAngle -- The horizontal angle of the 3D car model is within the range of [0, 360), with the front of the car facing upwards at 0 and increasing clockwise. Negative numbers indicate that this angle is not set
          vAngle -- The vertical angle of the 3D car model is within the range of [0, 90), with a 0 angle when viewed from above and a 90 angle when viewed from above. Negative numbers indicate that this angle is not set
* return  Success, 0
          Failed, Not 0
************************************************************************/
// internal               .
//           front        .
//             0          .  // look from front (pers-front view)
//      -------------     .
//     |             |    .
//     |             |    .
//     |             |    .
// 270 |             | 90 .
//     |             |    .
//     |             |    .
//     |             |    .
//      -------------     .
//          180           .  // look from rear (pers-rear view)
//         rear           .
// ****************************
// linux app input       .
//           front        .
//           180          .
//      -------------     .
//     |             |    .
//     |             |    .
//     |             |    .
// 270 |             | 90 .
//     |             |    .
//     |             |    .
//     |             |    .
//      -------------     .
//            0           .
//         rear           .
// internal = [(360-input)+180]%360
int com_linux_user::setCarRotateAngle(float hAngle, float vAngle)
{
    int l_ret = 0;
    XLOG_ERROR(g_AppContext, "[svs input]: hAngle is !!" << hAngle );
    XLOG_ERROR(g_AppContext, "[svs input]: vAngle is !!" << vAngle );

    if (static_cast<vfc::int32_t>(hAngle) < 0)
    {
        XLOG_ERROR(g_AppContext, "[svs]: hAngle received negative angles !!" );
    }
    else
    {
        if (cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleAzimuthDaddy_SenderPort.isConnected())
        {
            XLOG_ERROR(g_COMSocketContext, "DEBUG MEMPOOL setCarRotateAngle: sm_HUfreemodeAngleAzimuthDaddy_SenderPort.reserve() " << hAngle);
            auto& l_rData  = cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleAzimuthDaddy_SenderPort.reserve();
            l_rData.m_Data = ((360u - static_cast<vfc::uint32_t>(hAngle)) + 180u) % 360u;

            XLOG_ERROR(g_AppContext, "[svs]: setFreeModeAngle hAngle daddy is !!"
                                       << static_cast<int>(l_rData.m_Data) );
            cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleAzimuthDaddy_SenderPort.deliver();
        }
        else
        {
            XLOG_ERROR(g_AppContext, "[svs]: setFreeModeAngle hAngle daddy is not connected!!" );
            l_ret = -1;
        }
    }

    if (static_cast<vfc::int32_t>(vAngle) < 0)
    {
        XLOG_ERROR(g_AppContext, "[svs]: vAngle received negative angles !!" );
    }
    else
    {
        if (cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleElevationDaddy_SenderPort.isConnected())
        {
            XLOG_ERROR(g_COMSocketContext, "DEBUG MEMPOOL setCarRotateAngle: sm_HUfreemodeAngleElevationDaddy_SenderPort.reserve() " << vAngle);
            auto& l_rData  = cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleElevationDaddy_SenderPort.reserve();
            l_rData.m_Data = static_cast<vfc::uint32_t>(vAngle);

            XLOG_ERROR(g_AppContext, "[svs]: setFreeModeAngle vAngle daddy is !!"
                                       << static_cast<int>(l_rData.m_Data) );
            cc::daddy::CustomDaddyPorts::sm_HUfreemodeAngleElevationDaddy_SenderPort.deliver();
        }
        else
        {
            // XLOG_ERROR(g_AppContext, "[svs]: setFreeModeAngle vAngle daddy is not connected!!" );
            l_ret = -1;
        }
    }

    return l_ret;
}

/************************************************************************
* func   :getCarRotateAngle
* input  :hAngle -- The horizontal angle of the 3D car model is within the range of [0, 360), with the front of the car facing upwards at 0 and increasing clockwise. Negative numbers indicate that this angle is not set
          vAngle -- The vertical angle of the 3D car model is within the range of [0, 90), with a 0 angle when viewed from above and a 90 angle when viewed from above. Negative numbers indicate that this angle is not set
* return  Success, 0
          Failed, Not 0
************************************************************************/
int com_linux_user::getCarRotateAngle(float* hAngle, float* vAngle)
{
    if (nullptr != hAngle)
    {
        vfc::int32_t   l_hori                = ((360 - g_dataContainerFromSvs.m_returnFreemodeAngle) + 180) % 360;
        vfc::float32_t l_returnFreemodeAngle = static_cast<vfc::float32_t>(l_hori);
        *hAngle                              = l_returnFreemodeAngle;

        // XLOG_ERROR(g_AppContext,"[svs output]: m_returnFreemodeAngle - origin hori
        // is!!"<<g_dataContainerFromSvs.m_returnFreemodeAngle );
        XLOG_ERROR(g_AppContext, "[svs output]: m_returnFreemodeAngle - hori is!!" << l_returnFreemodeAngle
                                   );
    }
    if (nullptr != vAngle)
    {
        vfc::float32_t l_returnFreemodeAngleVert =
            static_cast<vfc::float32_t>(g_dataContainerFromSvs.m_returnFreemodeAngleVert);
        *vAngle = l_returnFreemodeAngleVert;
        XLOG_ERROR(g_AppContext, "[svs output]: m_returnFreemodeAngle - vert is!!" << l_returnFreemodeAngleVert
                                   );
    }

    // XLOG_ERROR(g_AppContext,"[svs]: m_returnFreemodeAngle - hori
    // is!!"<<g_dataContainerFromSvs.m_returnFreemodeAngle ); XLOG_ERROR(g_AppContext,"[svs]:
    // m_returnFreemodeAngle - vert is!!"<<g_dataContainerFromSvs.m_returnFreemodeAngleVert );

    return 0;
}

// Implement setRadarWallVisible function
int com_linux_user::setRadarWallVisible(bool visible)
{
    if (visible)
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_huRadarWallButton =
            cc::target::common::EhuRadarWallButton::OPENRADARWALL;
        g_dataContainerToSvs.m_LSMGData.m_pasStatus = cc::target::common::PAS_FrontRearActive;
    }
    else
    {
        g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_huRadarWallButton =
            cc::target::common::EhuRadarWallButton::CLOSERADARWALL;
        g_dataContainerToSvs.m_LSMGData.m_pasStatus = cc::target::common::PAS_Standby;
    }
    return 0;
}

int com_linux_user::setParkMode(AVMParkMode mode)
{
    // enum class AVMParkMode
    // {
    //     AVM_PARK_EXIT, // Exit parking mode
    //     AVM_PARK_360, // 360 mode
    //     AVM_PARK_LIMITED_HORIZONTAL, // Non-360 mode, horizontal
    //     AVM_PARK_LIMITED_VERTICAL, // Non-360 mode, vertical
    //     AVM_PARK_LIMITED_OBLIQUE, // Non-360 mode, tilted 45 degrees
    //     AVM_PARK_PARKING_START, // Vehicle control phase, start parking into target space
    //     AVM_PARK_PARKING_FINISH, // Parking into target space completed
    //     AVM_PARK_NON_STATIONARY, // Vehicle is in non-stationary state, do not display self-selected parking frame
    // };
    // enum class EFreeParkingStage : vfc::uint8_t
    // {
    //     None = 0u,      // not show any slot
    //     InFpStandstill, // show freeparking slot
    //     GuidanceStart,  // show target slot during guidance
    //     GuidanceFinish, // not show any slot
    //     InFpMoving      // not show any slot
    // };
    switch (mode)
    {
    case AVMParkMode::AVM_PARK_360:
    {
        g_dataContainerToSvs.m_parkhmiToSvs.m_freeParkingIn.m_parkStage =
            cc::target::common::EFreeParkingStage::InFpStandstill;
        g_dataContainerToSvs.m_parkhmiToSvs.m_parkingStage = cc::target::common::EParkingStage::FreeParking;
        g_dataContainerToSvs.m_parkhmiToSvs.m_freeParkingIn.m_is360FreeParking = true;
        break;
    }
    case AVMParkMode::AVM_PARK_LIMITED_HORIZONTAL:
    {
        g_dataContainerToSvs.m_parkhmiToSvs.m_freeParkingIn.m_parkStage =
            cc::target::common::EFreeParkingStage::InFpStandstill;
        g_dataContainerToSvs.m_parkhmiToSvs.m_parkingStage = cc::target::common::EParkingStage::FreeParking;
        g_dataContainerToSvs.m_parkhmiToSvs.m_freeParkingIn.m_is360FreeParking = false;
        g_dataContainerToSvs.m_parkhmiToSvs.m_freeParkingIn.m_slotType =
            cc::target::common::EFreeParkingSlotType::HorizontalSlot;
        break;
    }
    case AVMParkMode::AVM_PARK_LIMITED_VERTICAL:
    {
        g_dataContainerToSvs.m_parkhmiToSvs.m_freeParkingIn.m_parkStage =
            cc::target::common::EFreeParkingStage::InFpStandstill;
        g_dataContainerToSvs.m_parkhmiToSvs.m_parkingStage = cc::target::common::EParkingStage::FreeParking;
        g_dataContainerToSvs.m_parkhmiToSvs.m_freeParkingIn.m_is360FreeParking = false;
        g_dataContainerToSvs.m_parkhmiToSvs.m_freeParkingIn.m_slotType =
            cc::target::common::EFreeParkingSlotType::VerticalSlot;
        break;
    }
    case AVMParkMode::AVM_PARK_LIMITED_OBLIQUE:
    {
        g_dataContainerToSvs.m_parkhmiToSvs.m_freeParkingIn.m_parkStage =
            cc::target::common::EFreeParkingStage::InFpStandstill;
        g_dataContainerToSvs.m_parkhmiToSvs.m_parkingStage = cc::target::common::EParkingStage::FreeParking;
        g_dataContainerToSvs.m_parkhmiToSvs.m_freeParkingIn.m_is360FreeParking = false;
        g_dataContainerToSvs.m_parkhmiToSvs.m_freeParkingIn.m_slotType =
            cc::target::common::EFreeParkingSlotType::DiagonalSlot;
        break;
    }
    case AVMParkMode::AVM_PARK_PARKING_START:
    {
        g_dataContainerToSvs.m_parkhmiToSvs.m_parkingStage = cc::target::common::EParkingStage::FreeParking;
        g_dataContainerToSvs.m_parkhmiToSvs.m_freeParkingIn.m_parkStage =
            cc::target::common::EFreeParkingStage::GuidanceStart;
        break;
    }
    case AVMParkMode::AVM_PARK_PARKING_FINISH:
    {
        g_dataContainerToSvs.m_parkhmiToSvs.m_parkingStage = cc::target::common::EParkingStage::FreeParking;
        g_dataContainerToSvs.m_parkhmiToSvs.m_freeParkingIn.m_parkStage =
            cc::target::common::EFreeParkingStage::GuidanceFinish;
        break;
    }
    case AVMParkMode::AVM_PARK_NON_STATIONARY:
    {
        g_dataContainerToSvs.m_parkhmiToSvs.m_parkingStage = cc::target::common::EParkingStage::FreeParking;
        g_dataContainerToSvs.m_parkhmiToSvs.m_freeParkingIn.m_parkStage =
            cc::target::common::EFreeParkingStage::InFpMoving;
        break;
    }
    default:
    {
        g_dataContainerToSvs.m_parkhmiToSvs.m_parkingStage = cc::target::common::EParkingStage::Invalid;
        g_dataContainerToSvs.m_parkhmiToSvs.m_freeParkingIn.m_parkStage = cc::target::common::EFreeParkingStage::None;
        g_dataContainerToSvs.m_parkhmiToSvs.m_freeParkingIn.m_is360FreeParking = true;
        break;
    }
    }

    XLOG_ERROR(g_AppContext, "[svs]: setParkMode is !!" << static_cast<int>(mode) );

    return 0;
}

// typedef void (*ParkingSpaceSelectedCallback)(void *data, int size);
// data type is below, and size is not used, set it to 12=3*4:
// struct FreeParkingSlot  //output
// {
//     vfc::float32_t m_x;  //cm, position of freeparking slot picture center
//     vfc::float32_t m_y;  //cm
//     vfc::float32_t m_yaw;  //degree
// };
int com_linux_user::setParkingSpaceSelectedCb(ParkingSpaceSelectedCallback callback)
{
    g_dataContainerFromSvs.m_freeParkingSlotCallback = callback;
    return 0;
}

int com_linux_user::setSpaceSlotState(AVMParkSlotState state)
{
    static vfc::int32_t ctr = 0;
    g_dataContainerToSvs.m_parkhmiToSvs.m_freeParkingIn.m_isSlotParkable =
        static_cast<cc::target::common::EFreeParkingSlotState>(state);
    g_dataContainerToSvs.m_parkhmiToSvs.m_freeParkingIn.m_ctr = ++ctr;
    XLOG_ERROR(g_AppContext, "[svs]: setSpaceSlotState : " << static_cast<int>(state) );
    return 0;
}

int com_linux_user::setParkingRealTimeDataServiceData(AVMParkingSlotVertex vertexPos)
{
    // XLOG_ERROR(g_AppContext,"[svs]: setParkingRealTimeDataServiceData point1,m_x: " <<vertexPos.point1.m_x<<
    // XLOG_ENDL; XLOG_ERROR(g_AppContext,"[svs]: setParkingRealTimeDataServiceData point1,m_y: "
    // <<vertexPos.point1.m_y); XLOG_ERROR(g_AppContext,"[svs]: setParkingRealTimeDataServiceData
    // point2,m_x: " <<vertexPos.point2.m_x); XLOG_ERROR(g_AppContext,"[svs]:
    // setParkingRealTimeDataServiceData point2,m_y: " <<vertexPos.point2.m_y); XLOG_ERROR_OS(g_AppContext)
    // <<"[svs]: setParkingRealTimeDataServiceData point3,m_x: " <<vertexPos.point3.m_x);
    // XLOG_ERROR(g_AppContext,"[svs]: setParkingRealTimeDataServiceData point3,m_y: " <<vertexPos.point3.m_y<<
    // XLOG_ENDL; XLOG_ERROR(g_AppContext,"[svs]: setParkingRealTimeDataServiceData point4,m_x: "
    // <<vertexPos.point4.m_x); XLOG_ERROR(g_AppContext,"[svs]: setParkingRealTimeDataServiceData
    // point4,m_y: " <<vertexPos.point4.m_y);
    g_dataContainerToSvs.m_parkhmiToSvs.m_targetSlotPosition.m_point1.m_x = vertexPos.point1.m_x;
    g_dataContainerToSvs.m_parkhmiToSvs.m_targetSlotPosition.m_point1.m_y = vertexPos.point1.m_y;
    g_dataContainerToSvs.m_parkhmiToSvs.m_targetSlotPosition.m_point2.m_x = vertexPos.point2.m_x;
    g_dataContainerToSvs.m_parkhmiToSvs.m_targetSlotPosition.m_point2.m_y = vertexPos.point2.m_y;
    g_dataContainerToSvs.m_parkhmiToSvs.m_targetSlotPosition.m_point3.m_x = vertexPos.point3.m_x;
    g_dataContainerToSvs.m_parkhmiToSvs.m_targetSlotPosition.m_point3.m_y = vertexPos.point3.m_y;
    g_dataContainerToSvs.m_parkhmiToSvs.m_targetSlotPosition.m_point4.m_x = vertexPos.point4.m_x;
    g_dataContainerToSvs.m_parkhmiToSvs.m_targetSlotPosition.m_point4.m_y = vertexPos.point4.m_y;
    return 0;
}

void com_linux_user::setDoorStatus(AVMDoorType door, AVMState status)
{
    switch (static_cast<vfc::int32_t>(door))
    {
    case AVMDoorType::AVM_DOOR_FRONT_LEFT:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfDoorAndMirror.m_stateDoorSwitchFrontLeft =
            static_cast<vfc::uint8_t>(status);
        break;
    }
    case AVMDoorType::AVM_DOOR_FRONT_RIGHT:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfDoorAndMirror.m_stateDoorSwitchFrontRight =
            static_cast<vfc::uint8_t>(status);
        break;
    }
    case AVMDoorType::AVM_DOOR_BACK_LEFT:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfDoorAndMirror.m_stateDoorSwitchRearLeft =
            static_cast<vfc::uint8_t>(status);
        break;
    }
    case AVMDoorType::AVM_DOOR_BACK_RIGHT:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfDoorAndMirror.m_stateDoorSwitchRearRight =
            static_cast<vfc::uint8_t>(status);
        break;
    }
    case AVMDoorType::AVM_DOOR_FRONT_COVER:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfDoorAndMirror.m_statusContactFrontLid =
            static_cast<vfc::uint8_t>(status);
        break;
    }
    case AVMDoorType::AVM_DOOR_BACK_COVER:
    {
        g_dataContainerToSvs.m_StrippedPfValData.m_pfDoorAndMirror.m_statusTrunk = static_cast<vfc::uint8_t>(status);
        break;
    }
    // case AVMDoorType::AVM_REARVIEW_MIRROR:
    //     g_dataContainerToSvs.m_StrippedPfValData.m_pfDoorAndMirror.m_stateExteriorMirrorLeft = static_cast<vfc::uint8_t>(status);
    //     g_dataContainerToSvs.m_StrippedPfValData.m_pfDoorAndMirror.m_stateExteriorMirrorRight = static_cast<vfc::uint8_t>(status);
    //     break;
    default:
    {
        break;
    }
    }
    // XLOG_ERROR(g_AppContext,"[svs]: g_dataContainerToSvs.m_StrippedPfValData.m_pfDoorAndMirror is !!" <<
    // static_cast<int>(door) << static_cast<int>(status) );
}


void com_linux_user::setRearViewMirrorStatus(AVMRearViewMirrorState status)
{
    g_dataContainerToSvs.m_StrippedPfValData.m_pfDoorAndMirror.m_stateExteriorMirrorLeft = static_cast<vfc::uint8_t>(status);
    g_dataContainerToSvs.m_StrippedPfValData.m_pfDoorAndMirror.m_stateExteriorMirrorRight = static_cast<vfc::uint8_t>(status);
}

// Implement showDoorWarningWidth function
// show: true enable warning, false disable warning
// angle: door warning width angle
// carDoorClassify: which door needs warning operation 0: all doors 1: all left and right doors 2: trunk door
void com_linux_user::showDoorWarningWidth(bool /*show*/, float /*angle*/, int /*carDoorClassify*/)
{
    // TODO: Implement this function
    return;
}

// Implement setDoorAngle function
void com_linux_user::setDoorAngle(AVMDoorType /*door*/, float /*angle*/)
{
    // TODO: Implement this function
    return;
}

// Implement setDoorWidthIndicationStatus function
int com_linux_user::setDoorWidthIndicationStatus(AVMDoorType /*door*/, AVMState /*state*/)
{
    // TODO: Implement this function
    return -1;
}

// Set vehicle suspension height absolute value
//  mode: target suspension height mode
//  leftFront: left front suspension actual height, unit mm
//  rightFront: right front suspension actual height, unit mm
//  leftRear: left rear suspension actual height, unit mm
//  rightRear: right rear suspension actual height, unit mm
//  0: success, non-0: failure
int com_linux_user::setAirSuspensionHeight(int mode, int leftFront, int rightFront, int leftRear, int rightRear)
{
    g_dataContainerToSvs.m_StrippedCpjValData.m_airSuspensionHeight.m_mode      = static_cast<vfc::uint16_t>(mode);
    g_dataContainerToSvs.m_StrippedCpjValData.m_airSuspensionHeight.m_leftFront = static_cast<vfc::uint16_t>(leftFront);
    g_dataContainerToSvs.m_StrippedCpjValData.m_airSuspensionHeight.m_rightFront =
        static_cast<vfc::uint16_t>(rightFront);
    g_dataContainerToSvs.m_StrippedCpjValData.m_airSuspensionHeight.m_leftRear  = static_cast<vfc::uint16_t>(leftRear);
    g_dataContainerToSvs.m_StrippedCpjValData.m_airSuspensionHeight.m_rightRear = static_cast<vfc::uint16_t>(rightRear);
    return 0;
}

int com_linux_user::setSurround(int angle)
{
    // -360 ~360
    int64_t l_setAvmViewidTime = chrono_ms();
    XLOG_ERROR(g_AppContext, "[svs]: setSurroundTime is " << static_cast<vfc::int32_t>(l_setAvmViewidTime)
                               );
    XLOG_ERROR(g_AppContext, "[svs]: setSurround : " << static_cast<int>(angle) );

    if (cc::daddy::CustomDaddyPorts::sm_SurroundViewRotateAngle_SenderPort.isConnected())
    {
        XLOG_ERROR(g_COMSocketContext, "DEBUG MEMPOOL setSurround: sm_SurroundViewRotateAngle_SenderPort.reserve() " << angle);
        auto& l_container  = cc::daddy::CustomDaddyPorts::sm_SurroundViewRotateAngle_SenderPort.reserve();
        l_container.m_Data = angle;
        cc::daddy::CustomDaddyPorts::sm_SurroundViewRotateAngle_SenderPort.deliver();
        return 0;
    }
    return -1;
}

void com_linux_user::set3DCarZoomLevel(AVMZoomLevel zoomLevel)
{
    if (true == cc::daddy::CustomDaddyPorts::sm_ZoomLevel_SenderPort.isConnected())
    {
        XLOG_ERROR(g_COMSocketContext, "DEBUG MEMPOOL setSurround: sm_ZoomLevel_SenderPort.reserve() "  << static_cast<int>(zoomLevel));
        auto& l_rData = cc::daddy::CustomDaddyPorts::sm_ZoomLevel_SenderPort.reserve();
        switch (zoomLevel)
        {
        case AVMZoomLevel::AVM_ZOOM_LEVEL0:
        {
            l_rData.m_Data = cc::target::common::E3DZoomLevel::LEVEL0;
            break;
        }
        case AVMZoomLevel::AVM_ZOOM_LEVEL1:
        {
            l_rData.m_Data = cc::target::common::E3DZoomLevel::LEVEL1;
            break;
        }
        case AVMZoomLevel::AVM_ZOOM_LEVEL2:
        {
            l_rData.m_Data = cc::target::common::E3DZoomLevel::LEVEL2;
            break;
        }
        case AVMZoomLevel::AVM_ZOOM_LEVEL3:
        {
            l_rData.m_Data = cc::target::common::E3DZoomLevel::LEVEL3;
            break;
        }
        case AVMZoomLevel::AVM_ZOOM_LEVEL4:
        {
            l_rData.m_Data = cc::target::common::E3DZoomLevel::LEVEL4;
            break;
        }
        case AVMZoomLevel::AVM_ZOOM_LEVEL5:
        {
            l_rData.m_Data = cc::target::common::E3DZoomLevel::LEVEL5;
            break;
        }
        case AVMZoomLevel::AVM_ZOOM_LEVEL6:
        {
            l_rData.m_Data = cc::target::common::E3DZoomLevel::LEVEL6;
            break;
        }
        default:
        {
            l_rData.m_Data = cc::target::common::E3DZoomLevel::LEVEL3;
            break;
        }
        }
        cc::daddy::CustomDaddyPorts::sm_ZoomLevel_SenderPort.deliver();
    }
    return;
}

int com_linux_user::get3DCarZoomLevel(int* zoomLevel)
{
    if (!zoomLevel)
    {
        return -1;
    }
    *zoomLevel = g_dataContainerFromSvs.m_svs2ParkHmi.m_zoomLevel;
    return 0;
}

int com_linux_user::setTrailerLine(bool flag)
{
    g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_showTrailerLine = flag;
    return 0;
}

// Implement setBevFOV function
int com_linux_user::setBevFOV(bool fovScale)
{
    if (cc::daddy::CustomDaddyPorts::CustomDaddyPorts::sm_BirdEyeViewSwitch_SenderPort.isConnected())
    {
        XLOG_ERROR(g_COMSocketContext, "DEBUG MEMPOOL setBevFOV: sm_BirdEyeViewSwitch_SenderPort.reserve() " << fovScale);
        auto& l_container  = cc::daddy::CustomDaddyPorts::CustomDaddyPorts::sm_BirdEyeViewSwitch_SenderPort.reserve();
        l_container.m_Data = fovScale;
        cc::daddy::CustomDaddyPorts::CustomDaddyPorts::sm_BirdEyeViewSwitch_SenderPort.deliver();
        XLOG_ERROR(g_AppContext, "[svs]: setBevFOV: " << fovScale );
        return 0;
    }
    return -1;
}

// Implement setRemoveDistortion function
int com_linux_user::setRemoveDistortion(bool bRemoveDis)
{
    g_dataContainerToSvs.m_StrippedCpjValData.m_removeDistortion = bRemoveDis;
    XLOG_ERROR(g_AppContext, "[svs]: m_removeDistortion: " << static_cast<int>(bRemoveDis)
                               << " ; internal: " << g_dataContainerToSvs.m_StrippedCpjValData.m_removeDistortion
                               );

    return 0;
}

int com_linux_user::getPhysicalXDistance(int* distance, int x, int y)
{
    // TODO: Implement this function
    const vfc::int32_t   l_vehicleCenterX        = cc::assets::uielements::g_defaultSettings->m_hmCalibrateVehiclePos.x();
    // const vfc::int32_t   l_vehicleCentery        = cc::assets::uielements::g_defaultSettings->m_hmCalibrateVehiclePos.y();
    // const vfc::float32_t l_vehicleWidthPixel        =    292.0f * cc::assets::uielements::g_defaultSettings->m_scaleFactor; // 292 means the
    // const vfc::float32_t l_vehicleLengthPixel       =    632.0f * cc::assets::uielements::g_defaultSettings->m_scaleFactor;
    // const vfc::float32_t l_vehicleWidthWithMirror = pc::vehicle::g_mechanicalData->getWidthWithMirrors();
    // if (l_vehicleWidthPixel < 0.1)
    // {
    //     return -1;
    // }
    vfc::float32_t l_physicalPerPixel = cc::assets::uielements::g_defaultSettings->m_meterPerPixelHM;

    vfc::int32_t l_pixelDistanceX = std::abs(x - l_vehicleCenterX);
    // if (l_pixelDistanceX > 0.2 / l_physicalPerPixel)
    // {
    l_physicalPerPixel = l_physicalPerPixel * 1.05;
    // }

    *distance = std::round(l_pixelDistanceX * l_physicalPerPixel* 100.f );
    XLOG_ERROR(g_AppContext, "[svs]: getPhysicalXDistance:distance:  " << static_cast<int>(*distance)
                               << " ; x: " << x << " ; y: " << y << ";l_pixelDistanceX:" << l_pixelDistanceX
                               << " ;l_physicalPerPixel: " << l_physicalPerPixel
                               );
    return 0;
}
int com_linux_user::getWarningBoundCoordinate(BoundCoordinate* pBoundCoord, int left, int top, int right, int bottom)
{

    const vfc::int32_t l_deaultX = cc::assets::uielements::g_defaultSettings->m_hmCalibrateVehiclePos.x(); //864
    const vfc::int32_t l_deaultY = cc::assets::uielements::g_defaultSettings->m_hmCalibrateVehiclePos.y(); //964

    // const vfc::float32_t l_vehicleWidthWithMirror = pc::vehicle::g_mechanicalData->getWidthWithMirrors();
    // if (l_vehicleWidth < 0.1)
    // {
    //     return -1;
    // }
    vfc::float32_t l_physicalPerPixel = cc::assets::uielements::g_defaultSettings->m_meterPerPixelHM * 100;


    const vfc::float32_t l_vehicleWidth  = (pc::vehicle::g_mechanicalData->m_widthWithMirrors /l_physicalPerPixel) * 100;
    const vfc::float32_t l_vehicleHeight = ((pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear + pc::vehicle::g_mechanicalData->m_wheelbase + pc::vehicle::g_mechanicalData->m_axleToBumperDistanceFront) /l_physicalPerPixel) * 100;
    // convert physical to pixel
    auto l_left   = std::round(left / l_physicalPerPixel);
    auto l_top    = std::round(top / l_physicalPerPixel); //75
    auto l_right  = std::round(right / l_physicalPerPixel);
    auto l_bottom = std::round(bottom / l_physicalPerPixel);

    pBoundCoord->x1 = l_deaultX - l_left - l_vehicleWidth / 2;
    pBoundCoord->y1 = l_deaultY - l_top - l_vehicleHeight / 2; //858

    //944

    pBoundCoord->x2 = l_deaultX + l_right + l_vehicleWidth / 2;
    pBoundCoord->y2 = pBoundCoord->y1;

    pBoundCoord->x3 = pBoundCoord->x2;
    pBoundCoord->y3 = l_deaultY + l_bottom + l_vehicleHeight / 2;

    pBoundCoord->x4 = pBoundCoord->x1;
    pBoundCoord->y4 = pBoundCoord->y3;

    XLOG_ERROR(g_AppContext, "[svs]: getWarningBoundCoordinate:"
                            << ";hmCalibrateVehiclePos.x(): " << l_deaultX
                            << ";hmCalibrateVehiclePos.y(): " << l_deaultY
                            << ";l_vehicleWidth: " << l_vehicleWidth //2.2778
                            << ";l_vehicleHeight: " << l_vehicleHeight //5/98
                            << ";pBoundCoord->x1:  " << static_cast<int>( pBoundCoord->x1) //829
                            << ";pBoundCoord->y1:  " << static_cast<int>( pBoundCoord->y1) //858
                            << ";pBoundCoord->x2:  " << static_cast<int>( pBoundCoord->x2) //887
                            << ";pBoundCoord->y2:  " << static_cast<int>( pBoundCoord->y2) //858
                            << ";pBoundCoord->x3:  " << static_cast<int>( pBoundCoord->x3)
                            << ";pBoundCoord->y3:  " << static_cast<int>( pBoundCoord->y3) //1040
                            << ";pBoundCoord->x4:  " << static_cast<int>( pBoundCoord->x4) //829
                            << ";pBoundCoord->y4:  " << static_cast<int>( pBoundCoord->y4) //1040
                            << " ; left: " << left << " ; top: "<< top <<" ; right: "<<right <<" ; bottom: "<< bottom  //30 75 20 85
                            );
    return 0;
}
int com_linux_user::calPixelByPhysicalDistance(int* pixel, int distance)
{
    vfc::float32_t l_cmeterPerPixelHM = cc::assets::uielements::g_defaultSettings->m_meterPerPixelHM * 100;
    // if (distance > 20)
    // {
    l_cmeterPerPixelHM = l_cmeterPerPixelHM * 1.05;
    // }
    *pixel                                  = std::round(distance / l_cmeterPerPixelHM);
    XLOG_ERROR(g_AppContext, "[svs]: calPixelByPhysicalDistance:distance  " << static_cast<int>(distance)
                               << " :pixel"<< *pixel
                               );
    return 0;
}

// Implement getSDKVersion function
int com_linux_user::getSDKVersion(char* sdkVersion, int len)
{
    // TODO: Implement this function
    int versionLength = cc::views::nfsengineeringview::g_settings->m_version.length();
    if (len < versionLength + 1)
    {
        // The input buffer length is insufficient to hold the version number (+1 is for the string terminator '\0')
        return -1;
    }
    std::strcpy(sdkVersion, cc::views::nfsengineeringview::g_settings->m_version.c_str());

    XLOG_ERROR(g_AppContext,  "SDK 版本号 " << sdkVersion );

    return 0;
}

void com_linux_user::notifyAppStatus(bool status)
{
    XLOG_ERROR(g_AppContext, "[svs] AppStatus:  " << static_cast<vfc::int32_t>(status));
    if (status)
    {
        cc::vhm::g_enableVhm = true;
        cc::target::linux::g_enableIPC = true;
    }
    else
    {
        cc::vhm::g_enableVhm = false;
        cc::target::linux::g_enableIPC = false;
    }

    if (m_preAppStatus == false && status == true)
    {
        if (m_tranparentLevel !=  cc::core::g_transparentLevel)
        {
            XLOG_ERROR(g_AppContext, "[svs]: disable TransparentAnimation");
            cc::core::g_enableTransparentAnimation = false;
        }
        else
        {
            cc::core::g_enableTransparentAnimation = true;

        }

        XLOG_ERROR(g_AppContext, "[svs]: m_screemIDRequestPre: "<< m_screemIDRequestPre<<", m_screemIDRequestCur" <<m_screemIDRequestCur);
        if ((m_screemIDRequestPre == 8 /*EScreenID_SINGLE_FRONT_NORMAL*/ && m_screemIDRequestCur == 38 /*EScreenID_PERSPECTIVE_PRE*/)
                || (m_screemIDRequestPre == 38 /*EScreenID_PERSPECTIVE_PRE*/ && m_screemIDRequestCur == 8 /*EScreenID_SINGLE_FRONT_NORMAL*/ ))
        {
            XLOG_ERROR(g_AppContext, "[svs]: disable FadeAnimation");
            cc::core::g_enableFadeAnimation = false;
        }
        else
        {
            cc::core::g_enableFadeAnimation = true;
        }

    }



    m_preAppStatus = status;
}

// Implement setLogEnable function
void com_linux_user::setLogEnable(bool enable)
{
    // TODO: Implement this function
    std::cout << "setLogEnable !!!!!!!!!!!!!!!!!!!!!!!!!!++++++++++++++++++++++++++++++++++++++++++++++++++"
              << static_cast<int>(enable) << std::endl;
    pc::util::logging::Context::setGlobalLogEnableStatus(enable);
    return;
}

// Implement setLogCallback function
void com_linux_user::setLogCallback(CB_FUNC_LOG_PRINT callback)
{
    // TODO: Implement this function
    std::cout
        << "setLogCallback !!!!!!!!!!!!!!!!!!!!!!!!!!----------------------------------------------------------------"
        << std::endl;

#ifdef BYD_LINUX_LOG_FUNC_CALLBACK
    pc::util::logging::BYDLinuxLogger::m_bydLinuxLogFunc = callback;
#endif

    return;
}

int com_linux_user::setCrabAngles(CrabAngle angles)
{
    g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_CrabGuideline.m_isVisible =
        static_cast<bool>(angles.isShowguideline);
    g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_CrabGuideline.m_angle              = - angles.angle; // front wheel directions are special
    // g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_CrabGuideline.m_maxAngle           = angles.maxAngle;
    g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_CrabGuideline.m_leftRearWheelAngle = angles.leftrearwheelangle;
    g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_CrabGuideline.m_rightRearWheelAngle = angles.rightrearwheelangle;
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_CrabGuideline.m_isVisible is !!"
        << angles.isShowguideline);
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_CrabGuideline.m_angle is !!"
        << g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_CrabGuideline.m_angle);
    // XLOG_ERROR_OS(g_AppContext)
    //     << "[svs]: g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_CrabGuideline.m_maxAngle is !!"
    //     << g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_CrabGuideline.m_maxAngle );
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_CrabGuideline.m_leftRearWheelAngle is !!"
        << g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_CrabGuideline.m_leftRearWheelAngle);
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_CrabGuideline.m_rightRearWheelAngle is !!"
        << g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_CrabGuideline.m_rightRearWheelAngle);

    return 0;
}

void com_linux_user::setVotState(VotState status)
{
    if (status == VotState::VOT_NOREQUEST || status == VotState::VOT_REQUEST)
    {
        if (status == VotState::VOT_REQUEST)
        {
            g_dataContainerToSvs.m_parkhmiToSvs.m_specialParkingStatus = cc::target::common::ESpecialParkingStatus::ON;
        }
        else
        {
            g_dataContainerToSvs.m_parkhmiToSvs.m_specialParkingStatus = cc::target::common::ESpecialParkingStatus::OFF;
        }
    }

    XLOG_ERROR(g_AppContext,"[svs]: setVotState, status : " << static_cast<int> (status));
}

void com_linux_user::setVotRequestRotationDirection(VotDirRotation requestRotationDirection)
{
    if (static_cast<int>(requestRotationDirection) > static_cast<int>(VotDirRotation::VOT_ROTATION_INIT) && static_cast<int>(requestRotationDirection) < static_cast<int>(VotDirRotation::VOT_ROTATION_RESERVED))
    {
        g_dataContainerToSvs.m_parkhmiToSvs.m_rotationDirection = \
                    static_cast<cc::target::common::ERotationDirection>(requestRotationDirection);
    }
    XLOG_ERROR(g_AppContext,"[svs]: setVotRequestRotationDirection : " << static_cast<int>(requestRotationDirection));
}

void com_linux_user::setGoldLogo(bool isGold)
{
    g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_isGoldenLogo = isGold;
    XLOG_ERROR(g_AppContext, "[svs]: g_dataContainerToSvs.m_StrippedCpjValData.m_cpjHMI.m_isGoldenLogo is !!"
        << static_cast<int>(isGold));
}
} // namespace svs
