//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: LIA1LR David Liepelt (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CustomDataSync.cpp
/// @brief
//=============================================================================
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/target/linux/inc/CustomDataSync.h"

#include "cc/sm/viewmode/inc/ViewModexViewStateMachine_R2015b_types.h"
#include "cc/core/inc/CustomMechanicalData.h"

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/c2w/inc/Utils.h"
#include <sstream>

#include "pc/svs/util/math/inc/FloatComp.h"
#include "cc/assets/customfloorplategenerator/inc/CustomFloorPlateGenerator.h"

#include "osg/Math"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "valin/inc/valin_com2usm.hpp"

//#define ODO_COMPENSATE
using pc::util::logging::g_AppContext;
using pc::util::logging::g_COMSocketContext;

static pc::util::logging::Context g_svsRunnableContext("svsRunnable", "Data exchange between customer and svs");

#define DataSync_LOG(component_name) \
  if (g_customDataSyncSettings->m_log##component_name) XLOG_INFO_OS(g_svsRunnableContext)

#define DataSync_LOG_W(component_name) \
  if (g_customDataSyncSettings->m_log##component_name) XLOG_WARN_OS(g_svsRunnableContext)

namespace cc
{
namespace target
{
namespace linux
{

static constexpr float CM_2_M = 1.f / 100.f;
static constexpr float MM_2_M = 1.f / 1000.f;


//!
//! CustomDataSyncSettings
//!
class CustomDataSyncSettings : public pc::util::coding::ISerializable
{
public:

  CustomDataSyncSettings()
    : m_logVhmAbstOutput                  (false)
    , m_logLSMG                           (false)
    , m_logPasAPP                         (false)
    , m_logPasctl                         (false)
    , m_logParkctl                        (false)
    , m_logParkhmi                        (false)
    , m_logAciLmcOutput                   (false)
    , m_logMpsInData                      (false)
    , m_logMpsOutData                     (false)
    , m_logParkingLotsIn                  (false)
    , m_logPmaTravelDist                  (false)
    , m_logPmactlmrgr                     (false)
    , m_logValInOutputPf                  (false)
    , m_logValInOutputCpj                 (false)
    , m_logLcf                            (false)
    , m_logVariant                        (false)
    , m_logPIVI_ManualVideoSetupReq       (false)
    , m_logPIVI_DayNightThemeReq          (false)
    , m_logPIVI_ViewBufferStatusACK       (false)
    , m_logDefaultTrailerView             (false)
    , m_logGBCFeatureActivationStatus     (false)
    , m_logLSMGActivationSetStat          (false)
    , m_logPdmSetting                     (false)
    , m_logAebmgr                         (false)
    , m_logFusion                         (false)
    , m_logSitOcp                         (false)
    , m_logDiagRoutine                    (false)
    , m_logDegradationFids                (false)
    , m_logCalibrationStatus              (false)
    , m_logSignalErrorAndDegradation      (false)
    , m_logFreeparking                    (true)
  {
  }

  SERIALIZABLE(CustomDataSyncSettings)
  {
    ADD_BOOL_MEMBER(logVhmAbstOutput);
    ADD_BOOL_MEMBER(logLSMG);
    ADD_BOOL_MEMBER(logPasAPP);
    ADD_BOOL_MEMBER(logPasctl);
    ADD_BOOL_MEMBER(logParkctl);
    ADD_BOOL_MEMBER(logParkhmi);
    ADD_BOOL_MEMBER(logAciLmcOutput);
    ADD_BOOL_MEMBER(logMpsInData);
    ADD_BOOL_MEMBER(logMpsOutData);
    ADD_BOOL_MEMBER(logParkingLotsIn);
    ADD_BOOL_MEMBER(logPmaTravelDist);
    ADD_BOOL_MEMBER(logPmactlmrgr);
    ADD_BOOL_MEMBER(logValInOutputPf);
    ADD_BOOL_MEMBER(logValInOutputCpj);
    ADD_BOOL_MEMBER(logLcf);
    ADD_BOOL_MEMBER(logVariant);
    ADD_BOOL_MEMBER(logPIVI_ManualVideoSetupReq);
    ADD_BOOL_MEMBER(logPIVI_DayNightThemeReq);
    ADD_BOOL_MEMBER(logPIVI_ViewBufferStatusACK);
    ADD_BOOL_MEMBER(logDefaultTrailerView);
    ADD_BOOL_MEMBER(logGBCFeatureActivationStatus);
    ADD_BOOL_MEMBER(logLSMGActivationSetStat);
    ADD_BOOL_MEMBER(logPdmSetting);
    ADD_BOOL_MEMBER(logAebmgr);
    ADD_BOOL_MEMBER(logFusion);
    ADD_BOOL_MEMBER(logSitOcp);
    ADD_BOOL_MEMBER(logDiagRoutine);
    ADD_BOOL_MEMBER(logDegradationFids);
    ADD_BOOL_MEMBER(logCalibrationStatus);
    ADD_BOOL_MEMBER(logSignalErrorAndDegradation);
    ADD_BOOL_MEMBER(logFreeparking);
  }

  bool m_logVhmAbstOutput;
  bool m_logLSMG;
  bool m_logPasAPP;
  bool m_logPasctl;
  bool m_logParkctl;
  bool m_logParkhmi;
  bool m_logAciLmcOutput;
  bool m_logMpsInData;
  bool m_logMpsOutData;
  bool m_logParkingLotsIn;
  bool m_logPmaTravelDist;
  bool m_logPmactlmrgr;
  bool m_logValInOutputPf;
  bool m_logValInOutputCpj;
  bool m_logLcf;
  bool m_logVariant;
  bool m_logPIVI_ManualVideoSetupReq;
  bool m_logPIVI_DayNightThemeReq;
  bool m_logPIVI_ViewBufferStatusACK;
  bool m_logDefaultTrailerView;
  bool m_logGBCFeatureActivationStatus;
  bool m_logLSMGActivationSetStat;
  bool m_logPdmSetting;
  bool m_logAebmgr;
  bool m_logFusion;
  bool m_logSitOcp;
  bool m_logDiagRoutine;
  bool m_logDegradationFids;
  bool m_logCalibrationStatus;
  bool m_logSignalErrorAndDegradation;
  bool m_logFreeparking;
};

pc::util::coding::Item<CustomDataSyncSettings> g_customDataSyncSettings("CustomDataSync");


//!
//! LinuxDataSyncSetting
//!
class LinuxDataSyncSetting : public pc::util::coding::ISerializable
{
public:

  LinuxDataSyncSetting()
    : m_updateMask(~0u)
    , m_minDeltaExtCalibPos(0.01f)
    , m_minDeltaExtCalibAng(0.15f)
    , m_majorDeltaExtCalibAng(0.5f)
    , m_minDeltaIntrinsicFxFy(0.01f)
    , m_minDeltaIntrinsicCxCy(0.01f)
    , m_minDeltaIntrinsicK(0.01f)
    , m_timeDeltaMinorCalibUpdate_ms(2000)
    , m_timeDeltaMajorCalibUpdate_ms(500)
    , m_timeDeltaMasksUpdata_ms(3000)
    , m_averageFilterSize(0)
    , m_minVaildIntrinsicValue(50.f)
    , m_maxVaildIntrinsicValue(5000.f)
  {
  }

  SERIALIZABLE(LinuxDataSyncSetting)
  {
    ADD_UINT32_MEMBER(updateMask);
    ADD_FLOAT_MEMBER(minDeltaExtCalibPos);
    ADD_FLOAT_MEMBER(minDeltaExtCalibAng);
    ADD_FLOAT_MEMBER(majorDeltaExtCalibAng);
    ADD_FLOAT_MEMBER(minDeltaIntrinsicFxFy);
    ADD_FLOAT_MEMBER(minDeltaIntrinsicCxCy);
    ADD_FLOAT_MEMBER(minDeltaIntrinsicK);
    ADD_UINT32_MEMBER(timeDeltaMinorCalibUpdate_ms);
    ADD_UINT32_MEMBER(timeDeltaMajorCalibUpdate_ms);
    ADD_UINT32_MEMBER(timeDeltaMasksUpdata_ms);
    ADD_UINT32_MEMBER(averageFilterSize);
    ADD_FLOAT_MEMBER(minVaildIntrinsicValue);
    ADD_FLOAT_MEMBER(maxVaildIntrinsicValue);
  }

  unsigned int m_updateMask;
  float m_minDeltaExtCalibPos;
  float m_minDeltaExtCalibAng;
  float m_majorDeltaExtCalibAng;
  float m_minDeltaIntrinsicFxFy;
  float m_minDeltaIntrinsicCxCy;
  float m_minDeltaIntrinsicK;
  unsigned int m_timeDeltaMinorCalibUpdate_ms;
  unsigned int m_timeDeltaMajorCalibUpdate_ms;
  unsigned int m_timeDeltaMasksUpdata_ms;
  unsigned int m_averageFilterSize;
  float m_minVaildIntrinsicValue;
  float m_maxVaildIntrinsicValue;
};

static pc::util::coding::Item<LinuxDataSyncSetting> g_linuxDataSettings("LinuxDataSync");

//rbp_swcpma_run10ms.c
static Boolean rbp_getCurrentECUTime_1ms_b(rbp_Time_Type * f_ECUtime_ms_pu32)
{
    if (f_ECUtime_ms_pu32 == nullptr)
    {
        return false;
    }
    long            ms = 0; // Milliseconds
    time_t          s = 0;  // Seconds
    struct timespec spec;
    Boolean ret = false;
    //
    if(clock_gettime(CLOCK_MONOTONIC, &spec) == 0)
    {
        s  = spec.tv_sec;
        ms = round(spec.tv_nsec / 1.0e6); // Convert nanoseconds to milliseconds
        if (ms > 999) {
        s++;
        ms = 0;
        }
        ms += s*1000;
        *f_ECUtime_ms_pu32 = (rbp_Time_Type)ms;
        ret = true;
    }
    return ret;
}

static bool isCameraChange(const pc::c2w::IntrinsicCalibration& f_current, const pc::c2w::IntrinsicCalibration& f_new)
{
  // XLOG_INFO_OS(g_AppContext) <<"[Inside svs]: std::abs(f_current.getCx() -     f_new.getCx()) !!!!!" <<
  //                         std::abs(f_current.getCx() - f_new.getCx()) << XLOG_ENDL;

  // XLOG_INFO_OS(g_AppContext) <<"[Inside svs]: std::abs(f_current.getFy() -     f_new.getFy()) !!!!!" <<
  //                         std::abs(f_current.getFy() - f_new.getFy()) << XLOG_ENDL;
  return  (std::abs(f_current.getCx() -     f_new.getCx()) >= g_linuxDataSettings->m_minDeltaIntrinsicCxCy) ||
          (std::abs(f_current.getCy() -     f_new.getCy()) >= g_linuxDataSettings->m_minDeltaIntrinsicCxCy) ||
          (std::abs(f_current.getFx() -     f_new.getFx()) >= g_linuxDataSettings->m_minDeltaIntrinsicFxFy) ||
          (std::abs(f_current.getFy() -     f_new.getFy()) >= g_linuxDataSettings->m_minDeltaIntrinsicFxFy) ||
          (std::abs(f_current.getK1() -     f_new.getK1()) >= g_linuxDataSettings->m_minDeltaIntrinsicK) ||
          (std::abs(f_current.getK2() -     f_new.getK2()) >= g_linuxDataSettings->m_minDeltaIntrinsicK) ||
          (std::abs(f_current.getK3() -     f_new.getK3()) >= g_linuxDataSettings->m_minDeltaIntrinsicK) ||
          (std::abs(f_current.getK4() -     f_new.getK4()) >= g_linuxDataSettings->m_minDeltaIntrinsicK) ;
}

static bool isIntrinsicValid(const pc::c2w::IntrinsicCalibration f_new)
{
  // XLOG_INFO_OS(g_AppContext) <<"[Inside svs]: std::abs(f_current.getCx() -     f_new.getCx()) !!!!!" <<
  //                         std::abs(f_current.getCx() - f_new.getCx()) << XLOG_ENDL;


  const bool l_isIntrinsicValidBigEnough   =  (isGreaterEqual(f_new.getCx(),g_linuxDataSettings->m_minVaildIntrinsicValue)) ||
                                        (isGreaterEqual(f_new.getCy(),g_linuxDataSettings->m_minVaildIntrinsicValue)) ||
                                        (isGreaterEqual(f_new.getFx(),g_linuxDataSettings->m_minVaildIntrinsicValue)) ||
                                        (isGreaterEqual(f_new.getFy(),g_linuxDataSettings->m_minVaildIntrinsicValue)) ;

  const bool l_isIntrinsicValidSmallEnough =  (isLessEqual( f_new.getCx(), g_linuxDataSettings->m_maxVaildIntrinsicValue)) ||
                                        (isLessEqual( f_new.getCy(), g_linuxDataSettings->m_maxVaildIntrinsicValue)) ||
                                        (isLessEqual( f_new.getFx(), g_linuxDataSettings->m_maxVaildIntrinsicValue)) ||
                                        (isLessEqual( f_new.getFy(), g_linuxDataSettings->m_maxVaildIntrinsicValue)) ;

//   XLOG_INFO(g_AppContext, "[Inside svs]: l_isIntrinsicValidBigEnough"<<l_isIntrinsicValidBigEnough <<"l_isIntrinsicValidSmallEnough"<< l_isIntrinsicValidSmallEnough);

  return l_isIntrinsicValidBigEnough&&l_isIntrinsicValidSmallEnough;

}

//!
//! CustomDataSync
//!
CustomDataSync::CustomDataSync()
{
  cc::daddy::CustomDaddyPorts::sm_CameraPositionDaddySenderPort.connect( m_CameraPositionIPCReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_animationDaddy_SenderPort.connect( m_animationDaddy_IPCReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_camPosAxis2Rq_SenderPort.connect( m_camPosAxis2Rq_IPCReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_SvsToParkhmi_SenderPort.connect(m_SvsToParkhmi_IPCReceiver);
  cc::daddy::CustomDaddyPorts::sm_FreeParkingSlot_SenderPort.connect(m_FreeParkingSlot_IPCReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SlotSelectedId_SenderPort.connect(m_SlotSelectedId_IPCReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_ParkoutSelectedDirection_SenderPort.connect(m_ParkoutSelectedDirection_IPCReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_ZoomLevelIPC_SenderPort.connect(m_ZoomLevel_IPCReceiverPort);
}


CustomDataSync::~CustomDataSync()
{
  try
  {
  cc::daddy::CustomDaddyPorts::sm_CameraPositionDaddySenderPort.disconnect( m_CameraPositionIPCReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_animationDaddy_SenderPort.disconnect( m_animationDaddy_IPCReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_camPosAxis2Rq_SenderPort.disconnect( m_camPosAxis2Rq_IPCReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_SvsToParkhmi_SenderPort.disconnect(m_SvsToParkhmi_IPCReceiver);
  cc::daddy::CustomDaddyPorts::sm_FreeParkingSlot_SenderPort.disconnect(m_FreeParkingSlot_IPCReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_SlotSelectedId_SenderPort.disconnect(m_SlotSelectedId_IPCReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_ParkoutSelectedDirection_SenderPort.disconnect(m_ParkoutSelectedDirection_IPCReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_ZoomLevelIPC_SenderPort.disconnect(m_ZoomLevel_IPCReceiverPort);
  }
  catch(...)
  {}
}


void CustomDataSync::readVHMSignals(const cc::target::common::CVhmAbstOutputStripped& f_vhmAbstOutput)
{
  if (pc::daddy::BaseDaddyPorts::sm_OdometryDataDaddySenderPort.isConnected())
  {
    pc::daddy::OdometryDataDaddy& l_container = pc::daddy::BaseDaddyPorts::sm_OdometryDataDaddySenderPort.reserve();
    l_container.m_Data.m_xPos     = f_vhmAbstOutput.m_curOdometry_X;
    l_container.m_Data.m_yPos     = f_vhmAbstOutput.m_curOdometry_Y;
    l_container.m_Data.m_yawAngle = f_vhmAbstOutput.m_curOdometry_YawAngle;
    const int32_t l_x = static_cast<int32_t>(l_container.m_Data.m_xPos.value()*100.0f);
    // slogf(240322, _SLOG_INFO, "The vhm_x in Daddy is (cm): %d \n", l_x);
    l_container.m_Data.m_velocity = static_cast<vfc::CSI::si_metre_per_second_f32_t>(std::abs(f_vhmAbstOutput.m_velocity.value()));

    switch(static_cast<int>(m_convertedGear))
      {
        case pc::daddy::GEAR_D:
        {
          l_container.m_Data.m_vehMoveDir = pc::daddy::FORWARD;
          break;
        }
        case pc::daddy::GEAR_R:
        {
          l_container.m_Data.m_vehMoveDir = pc::daddy::BACKWARD;
          break;
        }
        default:
        {
          l_container.m_Data.m_vehMoveDir = pc::daddy::STANDSTILL;
          break;
        }
      }

    pc::daddy::BaseDaddyPorts::sm_OdometryDataDaddySenderPort.deliver();
  }

  // for VHM raw data
  if (cc::daddy::CustomDaddyPorts::sm_customVhmAbstRawDataDaddy_SenderPort.isConnected())
  {
    cc::daddy::CustomVhmAbstRaw_t& l_container = cc::daddy::CustomDaddyPorts::sm_customVhmAbstRawDataDaddy_SenderPort.reserve();
    l_container.m_Data.m_posXRaw  = f_vhmAbstOutput.m_posXRaw;
    l_container.m_Data.m_posYRaw  = f_vhmAbstOutput.m_posYRaw;
    l_container.m_Data.m_yawAngleRaw = f_vhmAbstOutput.m_yawAngleRaw;
    cc::daddy::CustomDaddyPorts::sm_customVhmAbstRawDataDaddy_SenderPort.deliver();
  }
}

// Using for normal USS overlay, not rader wall
void CustomDataSync::readLSMGSignals(const cc::target::common::LSMGDataSVS_st& f_LSMG_in)    // PRQA S 6041
{
  if (cc::daddy::CustomDaddyPorts::sm_customUltrasonicDataDaddySenderPort.isConnected())
  {
    pc::daddy::UltrasonicDataDaddy& l_CustomUsData = cc::daddy::CustomDaddyPorts::sm_customUltrasonicDataDaddySenderPort.reserve();

    // distance
    l_CustomUsData.m_Data[ 0u].setDistance( static_cast<float>(f_LSMG_in.LsmgDistanceDataFront[1u]) * CM_2_M );
    l_CustomUsData.m_Data[ 1u].setDistance( static_cast<float>(f_LSMG_in.LsmgDistanceDataFront[0u]) * CM_2_M );

    l_CustomUsData.m_Data[ 2u].setDistance( static_cast<float>(f_LSMG_in.LsmgDistanceDataLeft[3u]) * CM_2_M );
    l_CustomUsData.m_Data[ 3u].setDistance( static_cast<float>(f_LSMG_in.LsmgDistanceDataLeft[2u]) * CM_2_M );
    l_CustomUsData.m_Data[ 4u].setDistance( static_cast<float>(f_LSMG_in.LsmgDistanceDataLeft[1u]) * CM_2_M );
    l_CustomUsData.m_Data[ 5u].setDistance( static_cast<float>(f_LSMG_in.LsmgDistanceDataLeft[0u]) * CM_2_M );

    l_CustomUsData.m_Data[ 6u].setDistance( static_cast<float>(f_LSMG_in.LsmgDistanceDataRear[3u]) * CM_2_M );
    l_CustomUsData.m_Data[ 7u].setDistance( static_cast<float>(f_LSMG_in.LsmgDistanceDataRear[2u]) * CM_2_M );
    l_CustomUsData.m_Data[ 8u].setDistance( static_cast<float>(f_LSMG_in.LsmgDistanceDataRear[1u]) * CM_2_M );
    l_CustomUsData.m_Data[ 9u].setDistance( static_cast<float>(f_LSMG_in.LsmgDistanceDataRear[0u]) * CM_2_M );

    l_CustomUsData.m_Data[10u].setDistance( static_cast<float>(f_LSMG_in.LsmgDistanceDataRight[3u]) * CM_2_M );
    l_CustomUsData.m_Data[11u].setDistance( static_cast<float>(f_LSMG_in.LsmgDistanceDataRight[2u]) * CM_2_M );
    l_CustomUsData.m_Data[12u].setDistance( static_cast<float>(f_LSMG_in.LsmgDistanceDataRight[1u]) * CM_2_M );
    l_CustomUsData.m_Data[13u].setDistance( static_cast<float>(f_LSMG_in.LsmgDistanceDataRight[0u]) * CM_2_M );

    l_CustomUsData.m_Data[14u].setDistance( static_cast<float>(f_LSMG_in.LsmgDistanceDataFront[3u]) * CM_2_M );
    l_CustomUsData.m_Data[15u].setDistance( static_cast<float>(f_LSMG_in.LsmgDistanceDataFront[2u]) * CM_2_M );

    cc::daddy::CustomDaddyPorts::sm_customUltrasonicDataDaddySenderPort.deliver();
  }

}

#if USE_RADAR_WALL
namespace
{

void setUltrasonicData(
  pc::vehicle::UltrasonicData& f_ussData,
  const cc::target::common::PasAPPDataSVS_st::CDispAreaDist& f_displayArea,
  const unsigned int f_ussZoneBegin,
  const unsigned int f_numZones)
{

  for (unsigned int i = 0u; i < f_numZones; ++i)
  {
    const unsigned int l_zone = ((static_cast<unsigned int>(cc::target::sysconf::E_ULTRASONIC_NUM_ZONES) + f_ussZoneBegin) - i) % static_cast<unsigned int>(cc::target::sysconf::E_ULTRASONIC_NUM_ZONES);
    f_ussData[l_zone].setDistance(f_displayArea.m_dist2VehHighObj[i] * CM_2_M); //use high obj distance
    //f_ussData[l_zone].setDistance(f_displayArea.m_dist2Veh[i] * CM_2_M);
  }
}

constexpr unsigned int g_NUM_ZONES_FRONT_REAR = 12u;
constexpr unsigned int g_NUM_ZONES_LEFT_RIGHT = 8u;

} // namespace

void CustomDataSync::readPasAPPSignals(const cc::target::common::PasAPPDataSVS_st& f_pasAPP_in)
{
  if (pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.isConnected())
  {
    pc::daddy::UltrasonicDataDaddy& l_usData = pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.reserve();

    //! FRONT
    unsigned int l_zoneBegin = (g_NUM_ZONES_FRONT_REAR / 2u) - 1u;
    setUltrasonicData(l_usData.m_Data, f_pasAPP_in.m_front, l_zoneBegin, g_NUM_ZONES_FRONT_REAR);

    //! LEFT
    l_zoneBegin += g_NUM_ZONES_LEFT_RIGHT;
    setUltrasonicData(l_usData.m_Data, f_pasAPP_in.m_left, l_zoneBegin, g_NUM_ZONES_LEFT_RIGHT);

    //! REAR
    l_zoneBegin += g_NUM_ZONES_FRONT_REAR;
    setUltrasonicData(l_usData.m_Data, f_pasAPP_in.m_rear, l_zoneBegin, g_NUM_ZONES_FRONT_REAR);

    //! RIGHT
    l_zoneBegin += g_NUM_ZONES_LEFT_RIGHT;
    setUltrasonicData(l_usData.m_Data, f_pasAPP_in.m_right, l_zoneBegin, g_NUM_ZONES_LEFT_RIGHT);

    pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.deliver();
  }
}
#else
void CustomDataSync::readPasAPPSignals(const cc::target::common::PasAPPDataSVS_st& f_pasAPP_in)
{
  if (pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.isConnected())
  {
    pc::daddy::UltrasonicDataDaddy& l_usData = pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.reserve();

    // distance
    l_usData.m_Data[ 0u].setDistance( static_cast<float>(f_pasAPP_in.PasAPPDistanceAllObjDataFront[1u]) * CM_2_M );
    l_usData.m_Data[ 1u].setDistance( static_cast<float>(f_pasAPP_in.PasAPPDistanceAllObjDataFront[0u]) * CM_2_M );

    l_usData.m_Data[ 2u].setDistance( static_cast<float>(f_pasAPP_in.PasAPPDistanceAllObjDataLeft[3u]) * CM_2_M );
    l_usData.m_Data[ 3u].setDistance( static_cast<float>(f_pasAPP_in.PasAPPDistanceAllObjDataLeft[2u]) * CM_2_M );
    l_usData.m_Data[ 4u].setDistance( static_cast<float>(f_pasAPP_in.PasAPPDistanceAllObjDataLeft[1u]) * CM_2_M );
    l_usData.m_Data[ 5u].setDistance( static_cast<float>(f_pasAPP_in.PasAPPDistanceAllObjDataLeft[0u]) * CM_2_M );

    l_usData.m_Data[ 6u].setDistance( static_cast<float>(f_pasAPP_in.PasAPPDistanceAllObjDataRear[3u]) * CM_2_M );
    l_usData.m_Data[ 7u].setDistance( static_cast<float>(f_pasAPP_in.PasAPPDistanceAllObjDataRear[2u]) * CM_2_M );
    l_usData.m_Data[ 8u].setDistance( static_cast<float>(f_pasAPP_in.PasAPPDistanceAllObjDataRear[1u]) * CM_2_M );
    l_usData.m_Data[ 9u].setDistance( static_cast<float>(f_pasAPP_in.PasAPPDistanceAllObjDataRear[0u]) * CM_2_M );

    l_usData.m_Data[10u].setDistance( static_cast<float>(f_pasAPP_in.PasAPPDistanceAllObjDataRight[3u]) * CM_2_M );
    l_usData.m_Data[11u].setDistance( static_cast<float>(f_pasAPP_in.PasAPPDistanceAllObjDataRight[2u]) * CM_2_M );
    l_usData.m_Data[12u].setDistance( static_cast<float>(f_pasAPP_in.PasAPPDistanceAllObjDataRight[1u]) * CM_2_M );
    l_usData.m_Data[13u].setDistance( static_cast<float>(f_pasAPP_in.PasAPPDistanceAllObjDataRight[0u]) * CM_2_M );

    l_usData.m_Data[14u].setDistance( static_cast<float>(f_pasAPP_in.PasAPPDistanceAllObjDataFront[3u]) * CM_2_M );
    l_usData.m_Data[15u].setDistance( static_cast<float>(f_pasAPP_in.PasAPPDistanceAllObjDataFront[2u]) * CM_2_M );

    // set on path
    l_usData.m_Data[ 0u].setOnPath( f_pasAPP_in.PasAPPThreatPresentFront[1u] );
    l_usData.m_Data[ 1u].setOnPath( f_pasAPP_in.PasAPPThreatPresentFront[0u] );

    l_usData.m_Data[ 2u].setOnPath( f_pasAPP_in.PasAPPThreatPresentLeft[3u] );
    l_usData.m_Data[ 3u].setOnPath( f_pasAPP_in.PasAPPThreatPresentLeft[2u] );
    l_usData.m_Data[ 4u].setOnPath( f_pasAPP_in.PasAPPThreatPresentLeft[1u] );
    l_usData.m_Data[ 5u].setOnPath( f_pasAPP_in.PasAPPThreatPresentLeft[0u] );

    l_usData.m_Data[ 6u].setOnPath( f_pasAPP_in.PasAPPThreatPresentRear[3u] );
    l_usData.m_Data[ 7u].setOnPath( f_pasAPP_in.PasAPPThreatPresentRear[2u] );
    l_usData.m_Data[ 8u].setOnPath( f_pasAPP_in.PasAPPThreatPresentRear[1u] );
    l_usData.m_Data[ 9u].setOnPath( f_pasAPP_in.PasAPPThreatPresentRear[0u] );

    l_usData.m_Data[10u].setOnPath( f_pasAPP_in.PasAPPThreatPresentRight[3u] );
    l_usData.m_Data[11u].setOnPath( f_pasAPP_in.PasAPPThreatPresentRight[2u] );
    l_usData.m_Data[12u].setOnPath( f_pasAPP_in.PasAPPThreatPresentRight[1u] );
    l_usData.m_Data[13u].setOnPath( f_pasAPP_in.PasAPPThreatPresentRight[0u] );

    l_usData.m_Data[14u].setOnPath( f_pasAPP_in.PasAPPThreatPresentFront[3u] );
    l_usData.m_Data[15u].setOnPath( f_pasAPP_in.PasAPPThreatPresentFront[2u] );

    pc::daddy::BaseDaddyPorts::sm_ultrasonicDataDaddySenderPort.deliver();
  }
}
#endif

void CustomDataSync::readPfValSignals(const cc::target::common::CValInOutputPfStripped& f_data_in)  // PRQA S 6040  // PRQA S 6041  // PRQA S 6043
{
  // DoorAndMirror
  {
    if ( pc::daddy::BaseDaddyPorts::sm_DoorOpenDaddySenderPort.isConnected() )
    {
      // Preventing the log flood
      //static uint8_t l_prevFrontLeft  = f_data_in.m_pfDoorAndMirror.m_stateDoorSwitchFrontLeft;
      //static uint8_t l_prevFrontRight = f_data_in.m_pfDoorAndMirror.m_stateDoorSwitchFrontRight;
      //static uint8_t l_prevRearRight  = f_data_in.m_pfDoorAndMirror.m_stateDoorSwitchRearRight;
      //static uint8_t l_prevRearLeft   = f_data_in.m_pfDoorAndMirror.m_stateDoorSwitchRearLeft;

      pc::daddy::DoorStateDaddy& l_doorState = pc::daddy::BaseDaddyPorts::sm_DoorOpenDaddySenderPort.reserve();

      if (1u == f_data_in.m_pfDoorAndMirror.m_stateDoorSwitchFrontLeft)  //AVM_CLOSE, AVM_OPEN, AVM_NONE
      {
        l_doorState.m_Data[pc::daddy::CARDOOR_FRONT_LEFT] = pc::daddy::CARDOORSTATE_OPEN;
      }
      else //otherwise closed also is the data is invalid
      {
        l_doorState.m_Data[pc::daddy::CARDOOR_FRONT_LEFT] = pc::daddy::CARDOORSTATE_CLOSED;
      }

      if (1u == f_data_in.m_pfDoorAndMirror.m_stateDoorSwitchFrontRight)
      {
        l_doorState.m_Data[pc::daddy::CARDOOR_FRONT_RIGHT] = pc::daddy::CARDOORSTATE_OPEN;
      }
      else
      {
        l_doorState.m_Data[pc::daddy::CARDOOR_FRONT_RIGHT] = pc::daddy::CARDOORSTATE_CLOSED;
      }

      if (1u == f_data_in.m_pfDoorAndMirror.m_stateDoorSwitchRearRight)
      {
        l_doorState.m_Data[pc::daddy::CARDOOR_REAR_RIGHT] = pc::daddy::CARDOORSTATE_OPEN;
      }
      else
      {
        l_doorState.m_Data[pc::daddy::CARDOOR_REAR_RIGHT] = pc::daddy::CARDOORSTATE_CLOSED;
      }

      if (1u == f_data_in.m_pfDoorAndMirror.m_stateDoorSwitchRearLeft)
      {
        l_doorState.m_Data[pc::daddy::CARDOOR_REAR_LEFT] = pc::daddy::CARDOORSTATE_OPEN;
      }
      else
      {
        l_doorState.m_Data[pc::daddy::CARDOOR_REAR_LEFT] = pc::daddy::CARDOORSTATE_CLOSED;
      }

      if (1u == f_data_in.m_pfDoorAndMirror.m_statusContactFrontLid)
      {
        l_doorState.m_Data[pc::daddy::CARDOOR_HOOD] = pc::daddy::CARDOORSTATE_OPEN;
      }
      else
      {
        l_doorState.m_Data[pc::daddy::CARDOOR_HOOD] = pc::daddy::CARDOORSTATE_CLOSED;
      }

      if (1u == f_data_in.m_pfDoorAndMirror.m_statusTrunk)
      {
        // XLOG_INFO_OS(g_AppContext) <<"[Inside svs]: m_statusTrunk is open !!" << static_cast<int> (f_data_in.m_pfDoorAndMirror.m_statusTrunk) << XLOG_ENDL;
        l_doorState.m_Data[pc::daddy::CARDOOR_TRUNK] = pc::daddy::CARDOORSTATE_OPEN;
      }
      else
      {
        l_doorState.m_Data[pc::daddy::CARDOOR_TRUNK] = pc::daddy::CARDOORSTATE_CLOSED;
      }

      pc::daddy::BaseDaddyPorts::sm_DoorOpenDaddySenderPort.deliver();
    }

    // Mirror
    if (pc::daddy::BaseDaddyPorts::sm_SideMirrorFlapDaddySenderPort.isConnected())
    {
      pc::daddy::MirrorStateDaddy& l_mirrorsState = pc::daddy::BaseDaddyPorts::sm_SideMirrorFlapDaddySenderPort.reserve();
      // left mirror state
      if (1u == f_data_in.m_pfDoorAndMirror.m_stateExteriorMirrorLeft)
      {
        l_mirrorsState.m_Data[static_cast<unsigned int>(pc::daddy::SIDEMIRROR_LEFT)] = pc::daddy::MIRRORSTATE_FLAPPED;
      }
      else
      {
        l_mirrorsState.m_Data[static_cast<unsigned int>(pc::daddy::SIDEMIRROR_LEFT)] = pc::daddy::MIRRORSTATE_NOT_FLAPPED;
      }
      // right mirror state
      if (1u == f_data_in.m_pfDoorAndMirror.m_stateExteriorMirrorRight)
      {
        l_mirrorsState.m_Data[static_cast<unsigned int>(pc::daddy::SIDEMIRROR_RIGHT)] = pc::daddy::MIRRORSTATE_FLAPPED;
      }
      else
      {
        l_mirrorsState.m_Data[static_cast<unsigned int>(pc::daddy::SIDEMIRROR_RIGHT)] = pc::daddy::MIRRORSTATE_NOT_FLAPPED;
      }

      pc::daddy::BaseDaddyPorts::sm_SideMirrorFlapDaddySenderPort.deliver();
    }
  } // DoorAndMirror


// Steering
  {
    // //! Vehicle Steering Angle ***************************************************************************************************
    // if ( pc::daddy::BaseDaddyPorts::sm_SteeringAngleFrontDaddySenderPort.isConnected() )
    // {
    //   pc::daddy::SteeringAngleDaddy& l_container = pc::daddy::BaseDaddyPorts::sm_SteeringAngleFrontDaddySenderPort.reserve();
    //   l_container.m_Data = f_data_in.m_frontWheelAngle ;
    //   // XLOG_INFO_OS(g_AppContext) <<"[Inside svs]: m_frontWheelAngle is " <<f_data_in.m_frontWheelAngle.value()<< XLOG_ENDL;
    //   pc::daddy::BaseDaddyPorts::sm_SteeringAngleFrontDaddySenderPort.deliver();
    // }

    // if ( true == cc::daddy::CustomDaddyPorts::sm_DriverSteeringWheelAngleDaddy_SenderPort.isConnected() )
    // {
    //   auto& l_rData = cc::daddy::CustomDaddyPorts::sm_DriverSteeringWheelAngleDaddy_SenderPort.reserve();
    //   l_rData.m_Data = static_cast<float>(f_data_in.m_driverSteeringWheelAngle.value());
    //   cc::daddy::CustomDaddyPorts::sm_DriverSteeringWheelAngleDaddy_SenderPort.deliver();
    // }

    if ( true == pc::daddy::BaseDaddyPorts::sm_SteeringAngleRearDaddySenderPort.isConnected() )
    {
      auto& l_rData = pc::daddy::BaseDaddyPorts::sm_SteeringAngleRearDaddySenderPort.reserve();
      l_rData.m_Data = f_data_in.m_rearWheelAngle;
      cc::daddy::CustomDaddyPorts::sm_SteeringAngleRearDaddySenderPort.deliver();
    }

  } // Steering


// Gear
//   {
//     //! Vehicle Gear  ***************************************************************************************************
//     if ( pc::daddy::BaseDaddyPorts::sm_gearSenderPort.isConnected() )
//     {
//       pc::daddy::GearDaddy& l_container = pc::daddy::BaseDaddyPorts::sm_gearSenderPort.reserve();
//       pc::daddy::EGear l_convertedData = pc::daddy::GEAR_INIT ;

//       // EGearStatus
//       // GEAR_PARK     = 0,
//       // GEAR_REVERSE  = 1,
//       // GEAR_NEUTRAL  = 2,
//       // GEAR_DRIVE    = 3,
//       // GEAR_NOSIGNAL = 4,

//       switch(static_cast<int>(f_data_in.m_gearStatus))
//       {
//         case cc::target::common::GEAR_PARK :
//         {
//           l_convertedData = pc::daddy::GEAR_P;
//         } break;
//         case cc::target::common::GEAR_REVERSE :
//         {
//           l_convertedData = pc::daddy::GEAR_R;
//         } break;
//         case cc::target::common::GEAR_NEUTRAL :
//         {
//           l_convertedData = pc::daddy::GEAR_N;
//         } break;
//         case cc::target::common::GEAR_DRIVE :
//         {
//           l_convertedData = pc::daddy::GEAR_D;
//         } break;
//         case cc::target::common::GEAR_NOSIGNAL :
//         {
//           l_convertedData = pc::daddy::GEAR_INIT;
//         } break;
//         default:
//         {
//           //unknown value
//           l_convertedData = pc::daddy::GEAR_INVALID;
//         } break;
//       }

//       l_container.m_Data = l_convertedData;
//       pc::daddy::BaseDaddyPorts::sm_gearSenderPort.deliver();

//     }
//   } // Gear

// Odometry
  {
    //! Vehicle speed ***************************************************************************************************
    if ( pc::daddy::BaseDaddyPorts::sm_SpeedSenderPort.isConnected() )
    {
      pc::daddy::SpeedDaddy& l_container = pc::daddy::BaseDaddyPorts::sm_SpeedSenderPort.reserve();
      l_container.m_Data = 3.6f * f_data_in.m_vehicleVelocity.value(); // VehicleAnimation expecting Km/h
      pc::daddy::BaseDaddyPorts::sm_SpeedSenderPort.deliver();
    }

    //! Vehicle Driving Direction ***************************************************************************************************
    if ( pc::daddy::BaseDaddyPorts::sm_DrivingDirSenderPort.isConnected() )
    {
      pc::daddy::DrivingDirDaddy& l_rContainer = pc::daddy::BaseDaddyPorts::sm_DrivingDirSenderPort.reserve() ;

      if (cc::target::common::EVehicleDrvDir::DRVDIR_FWD == f_data_in.m_vehicleDrvDir) // 0 = Fwd
      {
          l_rContainer.m_Data = pc::daddy::DRVDIR_FORWARD;
      }
      else if (cc::target::common::EVehicleDrvDir::DRVDIR_BWD == f_data_in.m_vehicleDrvDir) // 0 = Bwd
      {
          l_rContainer.m_Data = pc::daddy::DRVDIR_BACKWARD;
      }
      else
      {
          l_rContainer.m_Data = pc::daddy::DRVDIR_NONE;
      }
      pc::daddy::BaseDaddyPorts::sm_DrivingDirSenderPort.deliver();
    }
  } // Odometry



// VehicleInfo
  {
    //! Vehicle Indicator Lever State ***************************************************************************************************
    if ( pc::daddy::BaseDaddyPorts::sm_indicatorStateSenderPort.isConnected() )
    {
      pc::daddy::IndicatorStateDaddy& l_indicatorState = pc::daddy::BaseDaddyPorts::sm_indicatorStateSenderPort.reserve();

      pc::daddy::EIndicatorState l_svsState = pc::daddy::INDICATOR_OFF;

      switch(f_data_in.m_indicatorStatus)
      {
        case cc::target::common::EIndicatorStatus::INDICATE_RIGHT:
        {
          l_svsState = pc::daddy::INDICATOR_RIGHT;
          break;
        }
        case cc::target::common::EIndicatorStatus::INDICATE_LEFT:
        {
          l_svsState = pc::daddy::INDICATOR_LEFT;
          break;
        }
        case cc::target::common::EIndicatorStatus::INDICATE_IDLE:
        {
          l_svsState = pc::daddy::INDICATOR_OFF;
          break;
        }
        case cc::target::common::EIndicatorStatus::INDICATE_WARNING:
        {
          l_svsState = pc::daddy::INDICATOR_WARN;
          break;
        }
        default:
        {
          break; // INDICATOR_OFF
        }
      }
      l_indicatorState.m_Data = l_svsState;
      pc::daddy::BaseDaddyPorts::sm_indicatorStateSenderPort.deliver();
    }
  } // VehicleInfo

} // readPfValSignals


void CustomDataSync::readCpjValSignals(const cc::target::common::StrippedCpjVal_st& f_StrippedCpjVal_in)  // PRQA S 6040  // PRQA S 6041
{
  // pc ports
  if ( true == pc::daddy::BaseDaddyPorts::sm_brakeLightStateSenderPort.isConnected() )
  {
    pc::daddy::BrakeLightStateDaddy& l_brakeLightState = pc::daddy::BaseDaddyPorts::sm_brakeLightStateSenderPort.reserve();

    pc::daddy::EBrakeLightState l_brakeLight = pc::daddy::BRAKE_LIGHT_OFF;
    if (1u == f_StrippedCpjVal_in.m_cpjPwrTm.m_brakeLampOnStatus)
    {
      l_brakeLight = pc::daddy::BRAKE_LIGHT_ON;
    }
    l_brakeLightState.m_Data = l_brakeLight;
    pc::daddy::BaseDaddyPorts::sm_brakeLightStateSenderPort.deliver();
  }

  if ( true == cc::daddy::CustomDaddyPorts::sm_PowerModeDaddy_SenderPort.isConnected() )
  {
    cc::daddy::PowerModeDaddy_t& l_powerMode = cc::daddy::CustomDaddyPorts::sm_PowerModeDaddy_SenderPort.reserve();
    l_powerMode.m_Data = static_cast<cc::daddy::EPowerMode>(f_StrippedCpjVal_in.m_cpjHMI.m_powerModeRaw);   // PRQA S 3013
    cc::daddy::CustomDaddyPorts::sm_PowerModeDaddy_SenderPort.deliver();
  }

  if ( true == cc::daddy::CustomDaddyPorts::sm_FCTARightDaddy_SenderPort.isConnected() )
  {
    cc::daddy::FCTARightDaddy_t& l_FCTARight = cc::daddy::CustomDaddyPorts::sm_FCTARightDaddy_SenderPort.reserve();
    l_FCTARight.m_Data = static_cast<cc::target::common::EFCTARightWarnLevel>(f_StrippedCpjVal_in.m_cpjHMI.m_FCTARightWarnlevel);  // PRQA S 3013
    cc::daddy::CustomDaddyPorts::sm_FCTARightDaddy_SenderPort.deliver();
  }

  if ( true == cc::daddy::CustomDaddyPorts::sm_FCTALeftDaddy_SenderPort.isConnected() )
  {
    cc::daddy::FCTALeftDaddy_t& l_FCTALeft = cc::daddy::CustomDaddyPorts::sm_FCTALeftDaddy_SenderPort.reserve();
    l_FCTALeft.m_Data = static_cast<cc::target::common::EFCTALeftWarnLevel>(f_StrippedCpjVal_in.m_cpjHMI.m_FCTALeftWarnlevel);   // PRQA S 3013
    cc::daddy::CustomDaddyPorts::sm_FCTALeftDaddy_SenderPort.deliver();
  }

  if ( true == cc::daddy::CustomDaddyPorts::sm_RCTARightDaddy_SenderPort.isConnected() )
  {
    cc::daddy::RCTARightDaddy_t& l_RCTARight = cc::daddy::CustomDaddyPorts::sm_RCTARightDaddy_SenderPort.reserve();
    l_RCTARight.m_Data = static_cast<cc::target::common::ERCTARightWarnLevel>(f_StrippedCpjVal_in.m_cpjHMI.m_RCTARightWarnlevel);   // PRQA S 3013
    cc::daddy::CustomDaddyPorts::sm_RCTARightDaddy_SenderPort.deliver();
  }

  if ( true == cc::daddy::CustomDaddyPorts::sm_RCTALeftDaddy_SenderPort.isConnected() )
  {
    cc::daddy::RCTALeftDaddy_t& l_RCTALeft = cc::daddy::CustomDaddyPorts::sm_RCTALeftDaddy_SenderPort.reserve();
    l_RCTALeft.m_Data = static_cast<cc::target::common::ERCTALeftWarnLevel>(f_StrippedCpjVal_in.m_cpjHMI.m_RCTALeftWarnlevel);   // PRQA S 3013
    cc::daddy::CustomDaddyPorts::sm_RCTALeftDaddy_SenderPort.deliver();
  }

  // cc light ports
  if ( true == cc::daddy::CustomDaddyPorts::sm_CustomVehicleLights_SenderPort.isConnected() )
  {
    cc::daddy::CustomVehicleLightsDaddy& l_vehicleLightState = cc::daddy::CustomDaddyPorts::sm_CustomVehicleLights_SenderPort.reserve();
    l_vehicleLightState.m_Data.m_lowBeamIndication  = f_StrippedCpjVal_in.m_cpjEnvVeh.m_lowBeamIndication;
    l_vehicleLightState.m_Data.m_mainBeamIndication = f_StrippedCpjVal_in.m_cpjEnvVeh.m_mainBeamIndication;
    l_vehicleLightState.m_Data.m_rearPosLightState  = f_StrippedCpjVal_in.m_cpjEnvVeh.m_rearPosLightState;
    l_vehicleLightState.m_Data.m_leftHeadLightState  = f_StrippedCpjVal_in.m_cpjEnvVeh.m_leftHeadLightState;
    l_vehicleLightState.m_Data.m_rightHeadLightState  = f_StrippedCpjVal_in.m_cpjEnvVeh.m_rightHeadLightState;
    l_vehicleLightState.m_Data.m_fogLightStatus  = f_StrippedCpjVal_in.m_cpjEnvVeh.m_fogLightStatus;

    l_vehicleLightState.m_Data.m_frontPosLightState             = f_StrippedCpjVal_in.m_cpjEnvVeh.m_frontPosLightState;
    l_vehicleLightState.m_Data.m_frontClearanceLightState       = f_StrippedCpjVal_in.m_cpjEnvVeh.m_frontClearanceLightState;
    l_vehicleLightState.m_Data.m_rearClearanceLightState        = f_StrippedCpjVal_in.m_cpjEnvVeh.m_rearClearanceLightState;
    l_vehicleLightState.m_Data.m_frontLeftCornerLightState      = f_StrippedCpjVal_in.m_cpjEnvVeh.m_frontLeftCornerLightState;
    l_vehicleLightState.m_Data.m_frontRightCornerLightState     = f_StrippedCpjVal_in.m_cpjEnvVeh.m_frontRightCornerLightState;
    l_vehicleLightState.m_Data.m_reverseLightState              = f_StrippedCpjVal_in.m_cpjEnvVeh.m_reverseLightState;

    if (1u == f_StrippedCpjVal_in.m_cpjEnvVeh.m_hazardLightState)
    {
      l_vehicleLightState.m_Data.m_turnSignalStatus = static_cast<vfc::uint8_t>(cc::target::common::EturnSignalstatus_::Turn_Signal_DangerWarningSignal);
    }
    else if (1u == f_StrippedCpjVal_in.m_cpjEnvVeh.m_leftIndicatorBlinkState)
    {
      l_vehicleLightState.m_Data.m_turnSignalStatus = static_cast<vfc::uint8_t>(cc::target::common::EturnSignalstatus_::Turn_Signal_LeftTurnSignalLightNormalFlashing);
    }
    else if (1u == f_StrippedCpjVal_in.m_cpjEnvVeh.m_rightIndicatorBlinkState)
    {
      l_vehicleLightState.m_Data.m_turnSignalStatus = static_cast<vfc::uint8_t>(cc::target::common::EturnSignalstatus_::Turn_Signal_RightTurnSignalLightFlashingNormally);
    }
    else
    {
      l_vehicleLightState.m_Data.m_turnSignalStatus = static_cast<vfc::uint8_t>(cc::target::common::EturnSignalstatus_::Turn_Signal_NOTWORKING);
    }
    cc::daddy::CustomDaddyPorts::sm_CustomVehicleLights_SenderPort.deliver();
    }

//   if ( true == cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.isConnected() )
//   {
//     cc::daddy::HUDislayModeSwitchDaddy_t& l_EScreenID = cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.reserve();
//     l_EScreenID.m_Data = f_StrippedCpjVal_in.m_cpjHMI.m_huDisplayID;   // PRQA S 3013
//     // XLOG_INFO_OS(g_AppContext) <<"[Inside svs]: setLayout is " <<static_cast<vfc::int32_t>(l_EScreenID.m_Data)<< XLOG_ENDL;
//     cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.deliver();
//   }

//   if ( true == cc::daddy::CustomDaddyPorts::sm_sideViewEnableStatus_SenderPort.isConnected() )
//   {
//     cc::daddy::SideViewEnableStatusDaddy& l_data = cc::daddy::CustomDaddyPorts::sm_sideViewEnableStatus_SenderPort.reserve();
//     l_data.m_Data = static_cast<cc::daddy::SideViewEnableStatus>(f_StrippedCpjVal_in.m_cpjHMI.m_sideEnableSts);   // PRQA S 3013
//     // XLOG_INFO_OS(g_AppContext) <<"[Inside svs]: m_sideEnableSts is " <<f_StrippedCpjVal_in.m_cpjHMI.m_sideEnableSts<< XLOG_ENDL;
//     cc::daddy::CustomDaddyPorts::sm_sideViewEnableStatus_SenderPort.deliver();
//   }

//   if ( true == cc::daddy::CustomDaddyPorts::sm_topViewEnableStatus_SenderPort.isConnected() )
//   {
//     cc::daddy::TopViewEnableStatusDaddy& l_data = cc::daddy::CustomDaddyPorts::sm_topViewEnableStatus_SenderPort.reserve();
//     l_data.m_Data = static_cast<cc::daddy::TopViewEnableStatus>(f_StrippedCpjVal_in.m_cpjHMI.m_topEnableSts);   // PRQA S 3013
//     cc::daddy::CustomDaddyPorts::sm_topViewEnableStatus_SenderPort.deliver();
//   }


  if ( true == cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.isConnected() )
  {
    auto& l_rData = cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.reserve();
    l_rData.m_Data.m_huX = f_StrippedCpjVal_in.m_cpjHMI.m_touchCoorX;
    l_rData.m_Data.m_huY = f_StrippedCpjVal_in.m_cpjHMI.m_touchCoorY;
    l_rData.m_Data.m_touchSwipeSpeed = f_StrippedCpjVal_in.m_cpjHMI.m_touchSwipeSpeed;
    l_rData.m_Data.m_huTwoFinger1X = f_StrippedCpjVal_in.m_cpjHMI.m_touch1CoorX;
    l_rData.m_Data.m_huTwoFinger1Y = f_StrippedCpjVal_in.m_cpjHMI.m_touch1CoorY;
    l_rData.m_Data.m_huTwoFinger2X = f_StrippedCpjVal_in.m_cpjHMI.m_touch2CoorX;
    l_rData.m_Data.m_huTwoFinger2Y = f_StrippedCpjVal_in.m_cpjHMI.m_touch2CoorY;
    // l_rData.m_Data.m_zoomFactorRq = static_cast<vfc::uint32_t>(f_StrippedCpjVal_in.m_zoomLevel);
    cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.deliver();
  }

  if ( true == cc::daddy::CustomDaddyPorts::sm_HUtouchEvenTypeDaddy_SenderPort.isConnected() )
  {
    auto& l_rData = cc::daddy::CustomDaddyPorts::sm_HUtouchEvenTypeDaddy_SenderPort.reserve();
    l_rData.m_Data = f_StrippedCpjVal_in.m_cpjHMI.m_touchEvenType;
    cc::daddy::CustomDaddyPorts::sm_HUtouchEvenTypeDaddy_SenderPort.deliver();
  }

  if ( true == cc::daddy::CustomDaddyPorts::sm_HUTwoFingerTouchEvenTypeDaddy_SenderPort.isConnected() )
  {
    auto& l_rData = cc::daddy::CustomDaddyPorts::sm_HUTwoFingerTouchEvenTypeDaddy_SenderPort.reserve();
    l_rData.m_Data = f_StrippedCpjVal_in.m_cpjHMI.m_twoFingerTouchEvenType;
          // XLOG_INFO_OS(g_AppContext) <<"[Inside svs]: sm_HUTwoFingerTouchEvenTypeDaddy_SenderPort:: " << static_cast<int>(f_StrippedCpjVal_in.m_cpjHMI.m_twoFingerTouchEvenType)<< XLOG_ENDL;

    cc::daddy::CustomDaddyPorts::sm_HUTwoFingerTouchEvenTypeDaddy_SenderPort.deliver();
  }

  if ( true == cc::daddy::CustomDaddyPorts::sm_HUvehTransReqDaddy_SenderPort.isConnected() )
  {
    auto& l_rData = cc::daddy::CustomDaddyPorts::sm_HUvehTransReqDaddy_SenderPort.reserve();
    l_rData.m_Data = f_StrippedCpjVal_in.m_cpjHMI.m_vehTransReq;
    cc::daddy::CustomDaddyPorts::sm_HUvehTransReqDaddy_SenderPort.deliver();
  }

  if ( true == cc::daddy::CustomDaddyPorts::sm_SVSVehColorAckDaddy_SenderPort.isConnected() )
  {
    auto& l_rData = cc::daddy::CustomDaddyPorts::sm_SVSVehColorAckDaddy_SenderPort.reserve();
    l_rData.m_Data = f_StrippedCpjVal_in.m_cpjHMI.m_vehColorReq;
    cc::daddy::CustomDaddyPorts::sm_SVSVehColorAckDaddy_SenderPort.deliver();
  }

  if ( true == cc::daddy::CustomDaddyPorts::sm_HUvehTransLevelDaddy_SenderPort.isConnected() )
  {
    auto& l_rData = cc::daddy::CustomDaddyPorts::sm_HUvehTransLevelDaddy_SenderPort.reserve();
    l_rData.m_Data = f_StrippedCpjVal_in.m_cpjHMI.m_vehTransLevel;
    cc::daddy::CustomDaddyPorts::sm_HUvehTransLevelDaddy_SenderPort.deliver();
  }


  if ( true == cc::daddy::CustomDaddyPorts::sm_CrabGuideline_SenderPort.isConnected() )
  {
    auto& l_rData = cc::daddy::CustomDaddyPorts::sm_CrabGuideline_SenderPort.reserve();
    l_rData.m_Data = f_StrippedCpjVal_in.m_cpjHMI.m_CrabGuideline;
    cc::daddy::CustomDaddyPorts::sm_CrabGuideline_SenderPort.deliver();
  }

  if ( true == cc::daddy::CustomDaddyPorts::sm_HURadarWallButtonDaddy_SenderPort.isConnected() )
  {
    auto& l_rData = cc::daddy::CustomDaddyPorts::sm_HURadarWallButtonDaddy_SenderPort.reserve();
    l_rData.m_Data = f_StrippedCpjVal_in.m_cpjHMI.m_huRadarWallButton;
    cc::daddy::CustomDaddyPorts::sm_HURadarWallButtonDaddy_SenderPort.deliver();
  }

  if ( true == cc::daddy::CustomDaddyPorts::sm_HUTrailerButtonDaddy_SenderPort.isConnected() )
  {
    auto& l_rData = cc::daddy::CustomDaddyPorts::sm_HUTrailerButtonDaddy_SenderPort.reserve();
    l_rData.m_Data = f_StrippedCpjVal_in.m_cpjHMI.m_showTrailerLine;
    cc::daddy::CustomDaddyPorts::sm_HUTrailerButtonDaddy_SenderPort.deliver();
  }

  if ( true == cc::daddy::CustomDaddyPorts::sm_AirSuspensionHeight_SenderPort.isConnected() )
  {
    auto& l_rData = cc::daddy::CustomDaddyPorts::sm_AirSuspensionHeight_SenderPort.reserve();
    l_rData.m_Data = f_StrippedCpjVal_in.m_airSuspensionHeight;
    cc::daddy::CustomDaddyPorts::sm_AirSuspensionHeight_SenderPort.deliver();
  }

//   if ( true == cc::daddy::CustomDaddyPorts::sm_ZoomLevel_SenderPort.isConnected() )
//   {
//     auto& l_rData = cc::daddy::CustomDaddyPorts::sm_ZoomLevel_SenderPort.reserve();
//     l_rData.m_Data = f_StrippedCpjVal_in.m_zoomLevel;
//     cc::daddy::CustomDaddyPorts::sm_ZoomLevel_SenderPort.deliver();
//   }

  if ( true == cc::daddy::CustomDaddyPorts::sm_RemoveDistortion_SenderPort.isConnected() )
  {
    auto& l_rData = cc::daddy::CustomDaddyPorts::sm_RemoveDistortion_SenderPort.reserve();
    l_rData.m_Data = f_StrippedCpjVal_in.m_removeDistortion;
    cc::daddy::CustomDaddyPorts::sm_RemoveDistortion_SenderPort.deliver();
  }

  if ( true == cc::daddy::CustomDaddyPorts::sm_GoldenEmblem_SenderPort.isConnected() )
  {
    auto& l_rData = cc::daddy::CustomDaddyPorts::sm_GoldenEmblem_SenderPort.reserve();
    l_rData.m_Data = f_StrippedCpjVal_in.m_cpjHMI.m_isGoldenLogo;
    cc::daddy::CustomDaddyPorts::sm_GoldenEmblem_SenderPort.deliver();
  }

  // bool l_leftUpdate = false;
  // bool l_rightUpdate = false;
  // bool l_frontUpdate = false;
  // bool l_rearUpdate = false;
  bool l_updateFlag = false;
  static std::array<bool, pc::core::sysconf::NUMBER_OF_CAMERAS_EXT> s_prevValidFlags = { {true, true, true, true, true} };
  static std::array<bool, pc::core::sysconf::NUMBER_OF_CAMERAS_EXT> s_prevSameFlags = { {false, false, false, false, false} };

  if (true == pc::daddy::BaseDaddyPorts::sm_cameraCalibrationDaddySenderPort.isConnected() && f_StrippedCpjVal_in.m_cameraParaFlag)
  {
    // XLOG_INFO_OS(g_AppContext) <<"[Inside svs]: into intrinisc check !!!!!" << XLOG_ENDL;
    // Check all the intrinsic daddy values is update or keep the same values
    for (unsigned int l_camIdx = 0; l_camIdx < pc::core::sysconf::NUMBER_OF_CAMERAS_EXT; ++l_camIdx)
    {

      const bool l_cameraFlag = isCameraChange(m_camArr[l_camIdx].getIntrinsicCalibration(),
                          f_StrippedCpjVal_in.m_cameraPara[l_camIdx].getIntrinsicCalibration());

      const bool l_validFlag = isIntrinsicValid(f_StrippedCpjVal_in.m_cameraPara[l_camIdx].getIntrinsicCalibration());
      if (l_validFlag != s_prevValidFlags[l_camIdx]) {
        if(!l_validFlag){
            XLOG_INFO(g_AppContext, "[Inside svs]: current camera: " << l_camIdx<<" intrinsic valid check failed !!!!!");
        }
        s_prevValidFlags[l_camIdx] = l_validFlag;
      }

      if (l_cameraFlag != s_prevSameFlags[l_camIdx]) {
        if(!l_cameraFlag){
            XLOG_INFO(g_AppContext, "[Inside svs]: current camera: " << l_camIdx<<" intrinsic is same !!!!!");
        }
        s_prevSameFlags[l_camIdx] = l_cameraFlag;
      }

      m_camArr[l_camIdx].setIntrinsicCalibration(f_StrippedCpjVal_in.m_cameraPara[l_camIdx].getIntrinsicCalibration());

      //update when at latest one camera intrinsic has changed
      l_updateFlag = (l_updateFlag || l_cameraFlag) && l_validFlag;

    }

    //If all intrinsic valuse are similar, just return
    if (l_updateFlag)
    {
      //! update calibration
      pc::daddy::CameraCalibrationDaddy& l_calib = pc::daddy::BaseDaddyPorts::sm_cameraCalibrationDaddySenderPort.reserveLastDelivery();

      for (unsigned int l_camIdx = 0; l_camIdx < pc::core::sysconf::NUMBER_OF_CAMERAS_EXT; ++l_camIdx)
      {

        l_calib.m_Data[l_camIdx].setIntrinsicCalibration(f_StrippedCpjVal_in.m_cameraPara[l_camIdx].getIntrinsicCalibration());

        //Will not update the extrinsic, so keep the init xml values
        // switch (l_camIdx)
        // {
        //   case pc::core::sysconf::EXT_FRONT_CAMERA:
        //     l_calib.m_Data[l_camIdx].setExtrinsicCalibration(pc::c2w::g_extCalibFront.data());
        //     break;
        //   case pc::core::sysconf::EXT_RIGHT_CAMERA:
        //     l_calib.m_Data[l_camIdx].setExtrinsicCalibration(pc::c2w::g_extCalibRight.data());
        //     break;
        //   case pc::core::sysconf::EXT_REAR_CAMERA:
        //     l_calib.m_Data[l_camIdx].setExtrinsicCalibration(pc::c2w::g_extCalibRear.data());
        //     break;
        //   case pc::core::sysconf::EXT_LEFT_CAMERA:
        //     l_calib.m_Data[l_camIdx].setExtrinsicCalibration(pc::c2w::g_extCalibLeft.data());
        //     break;
        //   case pc::core::sysconf::EXT_RIGHT_CAMERA_FOLDED:
        //     l_calib.m_Data[l_camIdx].setExtrinsicCalibration(pc::c2w::g_extCalibRightFolded.data());
        //     break;
        //   case pc::core::sysconf::EXT_LEFT_CAMERA_FOLDED:
        //     l_calib.m_Data[l_camIdx].setExtrinsicCalibration(pc::c2w::g_extCalibLeftFolded.data());
        //     break;
        //   default:
        //     break;
        // }

      }

      XLOG_INFO(g_AppContext, "[Inside svs]: new intrinsic has been assigned !!!!!");

      pc::daddy::BaseDaddyPorts::sm_cameraCalibrationDaddySenderPort.deliver();

      g_dataContainerToSvs.m_StrippedCpjValData.m_cameraParaFlag = false;
    }
  }
}

void CustomDataSync::readDoorSignals(const cc::target::common::CValInOutputPfStripped& f_data_in)  // PRQA S 6040  // PRQA S 6041
{
  if ( true == cc::daddy::CustomDaddyPorts::sm_DoorLockSts_SenderPort.isConnected() )
  {
    // lock sts not used in gac, bellow mapping is not correct.
    cc::daddy::DoorLockStsDaddy_t& l_DoorLockStsDaddy = cc::daddy::CustomDaddyPorts::sm_DoorLockSts_SenderPort.reserve();
    l_DoorLockStsDaddy.m_Data.m_FLdoorLockStatus = static_cast<cc::target::common::EStateDoorLock>(f_data_in.m_pfDoorAndMirror.m_stateDoorSwitchFrontLeft) ;
    l_DoorLockStsDaddy.m_Data.m_FRdoorLockStatus = static_cast<cc::target::common::EStateDoorLock>(f_data_in.m_pfDoorAndMirror.m_stateDoorSwitchFrontRight);
    l_DoorLockStsDaddy.m_Data.m_RLdoorLockStatus = static_cast<cc::target::common::EStateDoorLock>(f_data_in.m_pfDoorAndMirror.m_stateDoorSwitchRearLeft);
    l_DoorLockStsDaddy.m_Data.m_RRdoorLockStatus = static_cast<cc::target::common::EStateDoorLock>(f_data_in.m_pfDoorAndMirror.m_stateDoorSwitchRearRight);
    l_DoorLockStsDaddy.m_Data.m_TrunkLockStatus  = static_cast<cc::target::common::EStateDoorLock>(f_data_in.m_pfDoorAndMirror.m_statusTrunk);
    cc::daddy::CustomDaddyPorts::sm_DoorLockSts_SenderPort.deliver();
  }
}

void CustomDataSync::readParkhmiSignals( const cc::target::common::ParkhmiToSvs& f_parkhmi_in )  // PRQA S 6040  // PRQA S 6041
{
  if ( true == cc::daddy::CustomDaddyPorts::sm_ParkhmiToSvs_SenderPort.isConnected() )
  {
    cc::daddy::ParkhmiToSvs_t& l_data = cc::daddy::CustomDaddyPorts::sm_ParkhmiToSvs_SenderPort.reserve();
    l_data.m_Data.m_freeParkingIn = f_parkhmi_in.m_freeParkingIn;
    l_data.m_Data.m_targetSlotPosition = f_parkhmi_in.m_targetSlotPosition;
    l_data.m_Data.m_parkingStage = f_parkhmi_in.m_parkingStage;
    l_data.m_Data.m_specialParkingStatus = f_parkhmi_in.m_specialParkingStatus;
    l_data.m_Data.m_rotationDirection = f_parkhmi_in.m_rotationDirection;
    cc::daddy::CustomDaddyPorts::sm_ParkhmiToSvs_SenderPort.deliver();
  }
}

void CustomDataSync::inputVhmSignals(const cc::target::common::CValInOutputPfStripped& f_data_in)
{
  // XLOG_INFO_OS(g_AppContext) <<"[!!!!!!!!!!!!!!!!!]: inputVhmSignals !!" << XLOG_ENDL;
  //static int l_count_valin = 0;

  valin::pf_data.m_pfGear.m_gearStatus=static_cast<valin::EGearStatus>(f_data_in.m_gearStatus);

  valin::pf_data.m_pfOdometry.m_wheelDrvDirRR = g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRR;
  valin::pf_data.m_pfOdometry.m_wheelDrvDirRL = g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirRL;
  valin::pf_data.m_pfOdometry.m_wheelDrvDirFR = g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFR;
  valin::pf_data.m_pfOdometry.m_wheelDrvDirFL = g_dataContainerToSvs.m_StrippedPfValData.m_pfOdometry.m_wheelDrvDirFL;

  valin::pf_data.m_pfPmaFixedPoint.m_vehSpeed = static_cast<vfc::uint16_t >(f_data_in.m_vehicleVelocity.value() *3.6f*10.f); //m_vehSpeed unit km/h

  valin::pf_data.m_pfPmaFixedPoint.m_wheelRotationFL = (uint16_t)(f_data_in.m_pfOdometry.m_wheelRotationFL*2);
  valin::pf_data.m_pfPmaFixedPoint.m_wheelRotationFR = (uint16_t)(f_data_in.m_pfOdometry.m_wheelRotationFR*2);
  valin::pf_data.m_pfPmaFixedPoint.m_wheelRotationRL = (uint16_t)(f_data_in.m_pfOdometry.m_wheelRotationRL*2);
  valin::pf_data.m_pfPmaFixedPoint.m_wheelRotationRR = (uint16_t)(f_data_in.m_pfOdometry.m_wheelRotationRR*2);

  valin::pf_data.m_pfOdometry.m_wheelImpCtrFR = f_data_in.m_pfOdometry.m_wheelImpCtrFR;
  valin::pf_data.m_pfOdometry.m_wheelImpCtrFL = f_data_in.m_pfOdometry.m_wheelImpCtrFL;
  valin::pf_data.m_pfOdometry.m_wheelImpCtrRL = f_data_in.m_pfOdometry.m_wheelImpCtrRL;
  valin::pf_data.m_pfOdometry.m_wheelImpCtrRR = f_data_in.m_pfOdometry.m_wheelImpCtrRR;

  valin::pf_data.m_pfPmaFixedPoint.m_outsideTemp = 26.0f;
  valin::pf_data.m_pfTrailer.m_trailerState=false;
  valin::pf_data.m_pfTrailer.m_trailerHitchPresent=false;

  // valin::pf_data.m_pfTirePress.m_tirePressFL=static_cast<vfc::TSIUnitType<vfc::float32_t>::si_pascal_t>(com_megaIPC_user::g_inputSignalList.m_TirePressFL.load(std::memory_order_relaxed)*1000);
  // valin::pf_data.m_pfTirePress.m_tirePressFR=static_cast<vfc::TSIUnitType<vfc::float32_t>::si_pascal_t>(com_megaIPC_user::g_inputSignalList.m_TirePressFR.load(std::memory_order_relaxed)*1000);
  // valin::pf_data.m_pfTirePress.m_tirePressRL=static_cast<vfc::TSIUnitType<vfc::float32_t>::si_pascal_t>(com_megaIPC_user::g_inputSignalList.m_TirePressRL.load(std::memory_order_relaxed)*1000);
  // valin::pf_data.m_pfTirePress.m_tirePressRR=static_cast<vfc::TSIUnitType<vfc::float32_t>::si_pascal_t>(com_megaIPC_user::g_inputSignalList.m_TirePressRR.load(std::memory_order_relaxed)*1000);

  // rbp_Time_Type l_impCtrTimeStamp=static_cast<rbp_Time_Type>(com_megaIPC_user::g_inputSignalList.m_WheelSpeedPulse_Time.load(std::memory_order_relaxed));

  rbp_Time_Type l_ECUtime_ms_pu32 = 0;
  const bool l_isOk = rbp_getCurrentECUTime_1ms_b(&l_ECUtime_ms_pu32);
  const rbp_Time_Type l_systemTime = l_ECUtime_ms_pu32;

  valin::pf_data.m_pfOdometry.m_wheelImpCtrFRTimestamp = tic::CGlobalTimestamp(l_systemTime);
  valin::pf_data.m_pfOdometry.m_wheelImpCtrRRTimestamp = tic::CGlobalTimestamp(l_systemTime);
  valin::pf_data.m_pfOdometry.m_wheelImpCtrFLTimestamp = tic::CGlobalTimestamp(l_systemTime);
  valin::pf_data.m_pfOdometry.m_wheelImpCtrRLTimestamp = tic::CGlobalTimestamp(l_systemTime);

  // XLOG_INFO_OS(g_AppContext) <<"[Inside svs]: l_systemTime !!!!!" <<
  //                         l_systemTime << XLOG_ENDL;

  float l_steeringWheelAngle= f_data_in.m_driverSteeringWheelAngle.value();

  l_steeringWheelAngle=fabs(l_steeringWheelAngle)>800.0f?0.0f:l_steeringWheelAngle;
  valin::pf_data.m_pfSteering.m_steeringWheelAngle       = static_cast<vfc::CSI::si_degree_f32_t>(l_steeringWheelAngle);

  // rbp_Time_Type l_angleTimeStamp=static_cast<rbp_Time_Type>(com_megaIPC_user::g_inputSignalList.m_Steering_Time.load(std::memory_order_relaxed));
  valin::pf_data.m_pfSteering.m_frontWheelAngleTimestamp = tic::CGlobalTimestamp(l_systemTime);   // bus signal cycle time is 10ms

  //Using for vhm auto calib
  valin::pf_data.m_pfOdometry.m_yawRate                  = static_cast<vfc::CSI::si_degree_per_second_f32_t>(0.0);

  valin::pf_data.m_pfOdometry.m_longitudinalAcceleration = static_cast<vfc::CSI::si_metre_per_square_second_f32_t>(0.0f);
  valin::pf_data.m_pfOdometry.m_lateralAcceleration      = static_cast<vfc::CSI::si_metre_per_square_second_f32_t>(0.0f);

  // valin::pf_data.m_pfSteering.m_rearAxleSteeringAngle    = vfc::CSI::si_degree_f32_t(0.0f);
  valin::pf_data.m_pfSteering.m_rearAxleSteeringAngle    = f_data_in.m_rearWheelAngle;
  valin::pf_data.m_pfSteering.m_epsStatus                = valin::EEpsStatus::EPS_INACTIVE;
  valin::pf_data.m_pfSteering.m_epsStatusRas             = valin::EPS_RAS_NOT_PRESENT;

  // vfc::uint8_t l_mirror=com_megaIPC_user::g_inputSignalList.m_RightBodyLoda_1_ExtRMirFldSts_R_MirrorFoldUnfoldSt.load(std::memory_order_relaxed);

  // if(l_mirror==0){
  //     l_mirror=1;//MIRROR_FOLDED
  // }
  // else{
  //     l_mirror=0;//valin::EStateMirror::MIRROR_UNFOLDED;
  // }

  valin::pf_data.m_pfDoorAndMirror.m_stateExteriorMirrorLeft  = valin::EStateMirror::MIRROR_UNFOLDED;
  valin::pf_data.m_pfDoorAndMirror.m_stateExteriorMirrorRight = valin::EStateMirror::MIRROR_UNFOLDED;

  // //status check
  // vfc::uint8_t l_temp=com_megaIPC_user::g_inputSignalList.m_YRS_0x246_YRS_LgtAcce_St.load(std::memory_order_relaxed);
  // if(com_megaIPC_user::g_inputSignalList.m_YRS_0x246_YRS_LgtSnsrSts.load(std::memory_order_relaxed)!=1){
  //     l_temp=15;
  // }
  // valin::pf_data.m_pfOdometry.m_longitudinalAccelerationQualifier = static_cast<valin::ELongAccQualifier>(l_temp);

  //         l_temp=com_megaIPC_user::g_inputSignalList.m_YRS_0x242_YRS_LatAcce_St.load(std::memory_order_relaxed);
  // if(com_megaIPC_user::g_inputSignalList.m_YRS_0x242_YRS_LatSnsrSts.load(std::memory_order_relaxed)!=1){
  //     l_temp=15;
  // }
  // valin::pf_data.m_pfOdometry.m_lateralAccelerationQualifier      = static_cast<valin::ELateralAccQualifier>(l_temp);

  //         l_temp=com_megaIPC_user::g_inputSignalList.m_YRS_0x242_YRS_YawRateSt.load(std::memory_order_relaxed);
  // if(com_megaIPC_user::g_inputSignalList.m_YRS_0x242_YRS_YawRateSnsrSts.load(std::memory_order_relaxed)!=1||com_megaIPC_user::g_inputSignalList.m_YRS_0x242_YRS_YawRateCalSts.load(std::memory_order_relaxed)!=1){
  //     l_temp=15;
  // }
  // valin::pf_data.m_pfOdometry.m_qualifierYawVelocityVehicle       = static_cast<valin::EQualifierYawVelocityVehicle>(l_temp);


  valin::pf_data.m_pfSteering.m_rearWheelAngleStatus              = valin::REAR_STEERING_ANGLE_VALID;
  valin::pf_data.m_pfSteering.m_frontWheelAngleStatus             = valin::EFrontWheelAngleStatus::FWA_LOW_PRECISION;

  valin::pf_data.m_pfOdometry.m_wheelRotationFRQualifier          = static_cast<valin::EWheelRotationQualifier>(f_data_in.m_pfOdometry.m_wheelRotationFRQualifier);
  valin::pf_data.m_pfOdometry.m_wheelRotationFLQualifier          = static_cast<valin::EWheelRotationQualifier>(f_data_in.m_pfOdometry.m_wheelRotationFLQualifier);
  valin::pf_data.m_pfOdometry.m_wheelRotationRRQualifier          = static_cast<valin::EWheelRotationQualifier>(f_data_in.m_pfOdometry.m_wheelRotationRRQualifier);
  valin::pf_data.m_pfOdometry.m_wheelRotationRLQualifier          = static_cast<valin::EWheelRotationQualifier>(f_data_in.m_pfOdometry.m_wheelRotationRLQualifier);

  valin::pf_data.m_pfBrakes.m_espOpMode                           = valin::EEspOpMode::ABS_ASR_ESP_CTRL_INACTIVE;

  //Using for VHM2
  // if(com_megaIPC_user::g_inputSignalList.m_Driving_IGNStatus.load(std::memory_order_relaxed)==8&&com_megaIPC_user::g_inputSignalList.m_Driving_ACCStatus.load(std::memory_order_relaxed)==8){
  //     valin::pf_data.m_pfVehicleInfo.m_ignSwState                 =valin::EIgnSwState::IGN_SW_ST_OFF;
  // }
  // else if(com_megaIPC_user::g_inputSignalList.m_Driving_IGNStatus.load(std::memory_order_relaxed)==1){
  //     valin::pf_data.m_pfVehicleInfo.m_ignSwState                 =valin::EIgnSwState::IGN_SW_ST_ON;
  // }
  // else if(com_megaIPC_user::g_inputSignalList.m_Driving_ACCStatus.load(std::memory_order_relaxed)==1){
  //     valin::pf_data.m_pfVehicleInfo.m_ignSwState                 =valin::EIgnSwState::IGN_SW_ST_ACC;
  // }
  // else{
  //     valin::pf_data.m_pfVehicleInfo.m_ignSwState                 =valin::EIgnSwState::IGN_SW_ST_OFF;
  // }

  // valin::pf_data.m_pfVehicleInfo.m_uBatt=com_megaIPC_user::g_inputSignalList.m_FrontRoomLoad_1_LowBatVFb.load(std::memory_order_relaxed)*10;

  // valin::cpj_data.m_VehBlocked   = false;
}

bool CustomDataSync::writeDataFromSvs( cc::target::common::DataContainerFromSvs &f_dataFromSvs )  // PRQA S 6040  // PRQA S 6041  // PRQA S 6044  // PRQA S 2757
{
  // std::cout << "SV3D CustomDataSync :: writeDataFromSvs " << std::endl;
  bool l_ret = false;

  m_animationDaddy_IPCReceiver.update();
  m_camPosAxis2Rq_IPCReceiver.update();
  m_SvsToParkhmi_IPCReceiver.update();
  m_FreeParkingSlot_IPCReceiverPort.update();
  m_SlotSelectedId_IPCReceiverPort.update();
  m_ParkoutSelectedDirection_IPCReceiverPort.update();
  m_ZoomLevel_IPCReceiverPort.update();

  if (m_animationDaddy_IPCReceiver.isConnected())
  {
    if (m_animationDaddy_IPCReceiver.hasNewData())
    {
      const cc::daddy::EViewAnimationDaddy* const l_data = m_animationDaddy_IPCReceiver.getData();
      if(l_data != nullptr)
      {
        f_dataFromSvs.m_viewAnimationSt = l_data->m_Data;

        vfc::int32_t l_viewAnimation = static_cast<vfc::int32_t>(l_data->m_Data);
        if ( nullptr != f_dataFromSvs.m_slideCallback )
        {
          f_dataFromSvs.m_slideCallback(&l_viewAnimation, 4);
          // XLOG_INFO_OS(g_AppContext) <<"[CustomSync]: m_viewAnimation is !!" << static_cast<int> (l_data->m_Data) << XLOG_ENDL;
        }
        l_ret = true;
      }
    }
  }

  if (m_camPosAxis2Rq_IPCReceiver.isConnected())
  {
    if (m_camPosAxis2Rq_IPCReceiver.hasNewData())
    {
      const cc::daddy::CamPosAxis2RqDaddy* const l_data = m_camPosAxis2Rq_IPCReceiver.getData();
      if(l_data != nullptr)
      {
        // XLOG_INFO_OS(g_AppContext) <<"[CustomSync]: m_returnFreemodeAngle is !!" << static_cast<int> (l_data->m_Data.m_Vert) << XLOG_ENDL;
        // XLOG_INFO_OS(g_AppContext) <<"[CustomSync]: m_returnFreemodeAngle is !!" << static_cast<int> (l_data->m_Data.m_Vert) << XLOG_ENDL;
        f_dataFromSvs.m_returnFreemodeAngle = static_cast<vfc::int32_t>(l_data->m_Data.m_Hori);
        f_dataFromSvs.m_returnFreemodeAngleVert = static_cast<vfc::int32_t>(l_data->m_Data.m_Vert);
        l_ret = true;
      }
    }
  }

  if (m_FreeParkingSlot_IPCReceiverPort.isConnected())
  {
    if (m_FreeParkingSlot_IPCReceiverPort.hasNewData())
    {
      const cc::daddy::FreeParkingSlot_t* const l_data = m_FreeParkingSlot_IPCReceiverPort.getData();
      if(l_data != nullptr)
      {
        cc::target::common::FreeParkingSlot l_freeParkingSlot = l_data->m_Data;
        if ( nullptr != f_dataFromSvs.m_freeParkingSlotCallback )
        {
          XLOG_INFO(g_AppContext,"[CustomSync]: l_freeParkingSlot is !!" << l_freeParkingSlot.m_x);
          XLOG_INFO(g_AppContext,"[CustomSync]: l_freeParkingSlot is !!" << l_freeParkingSlot.m_y);
          XLOG_INFO(g_AppContext,"[CustomSync]: l_freeParkingSlot is !!" << l_freeParkingSlot.m_yaw);
          f_dataFromSvs.m_freeParkingSlotCallback(&l_freeParkingSlot, 12);
        }
        l_ret = true;
      }
    }
  }

  if (m_ZoomLevel_IPCReceiverPort.hasNewData())
  {
    const auto l_data = m_ZoomLevel_IPCReceiverPort.getData();
    f_dataFromSvs.m_svs2ParkHmi.m_zoomLevel = static_cast<vfc::int32_t>(l_data->m_Data);
    l_ret = true;
  }

  // TODO update selected parking slot

  // TODO update selected parkout direction

  m_animationDaddy_IPCReceiver.cleanup();
  m_camPosAxis2Rq_IPCReceiver.cleanup();
  m_SvsToParkhmi_IPCReceiver.cleanup();
  m_FreeParkingSlot_IPCReceiverPort.cleanup();
  m_SlotSelectedId_IPCReceiverPort.cleanup();
  m_ParkoutSelectedDirection_IPCReceiverPort.cleanup();
  m_ZoomLevel_IPCReceiverPort.cleanup();

  return l_ret;
}


} //namespace linux
} //namespace target
} //namespace cc
