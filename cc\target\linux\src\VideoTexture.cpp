//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

// #include "cc/imc/qualcomm/qcengine/inc/VideoTexture.h"
// #include "cc/imc/qualcomm/qcengine/inc/TextureEGLImageKHR.h"


#include "cc/target/linux/inc/VideoTexture.h"
#include "cc/target/linux/inc/TextureEGLImageKHR.h"

#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
// #include "pc/generic/util/logging/inc/Logging.h"

#include <cassert>

using pc::util::logging::g_EngineContext;
using pc::util::logging::g_StartupContext;

namespace pc
{
namespace target
{
namespace qualcomm
{

//!
//! VideoTexture
//!
VideoTextureQc::VideoTextureQc(osg::Texture::FilterMode f_minificationFilter, osg::Texture::FilterMode f_magnificationFilter)
{
  for(unsigned int i=0; i < cc::target::sysconf::E_SYSTEM_CONFIG_NUMBER_OF_CAMERAS_TEXTURE; ++i)
  {
    m_cameraTextures[i] = new TextureEGLImageKHR();
    m_cameraTextures[i]->setMinificationFilter(f_minificationFilter);
    m_cameraTextures[i]->setMagnificationFilter(f_magnificationFilter);

    m_cameraTexturesNearestNeighbor[i] = new TextureEGLImageKHR();
    m_cameraTexturesNearestNeighbor[i]->setMinificationFilter(osg::Texture::NEAREST);
    m_cameraTexturesNearestNeighbor[i]->setMagnificationFilter(osg::Texture::NEAREST);
  }
}

//must be called in render thread
void VideoTextureQc::deleteGLTextures()
{
  // for(unsigned int i=0; i < cc::target::sysconf::E_SYSTEM_CONFIG_NUMBER_OF_CAMERAS_TEXTURE; ++i)
  // {
  //   if(m_cameraTextures[i])
  //   {
  //     m_cameraTextures[i]->deleteGLTexture();
  //   }
  //   if(m_cameraTexturesNearestNeighbor[i])
  //   {
  //     m_cameraTexturesNearestNeighbor[i]->deleteGLTexture();
  //   }
  // }
}

void VideoTextureQc::updateEGLImageHandle(unsigned int f_camTex, EGLImageKHR f_eglImage)
{
  assert(f_camTex < m_cameraTextures.size());
  m_cameraTextures[f_camTex]->setEGLImage(f_eglImage);

  // XLOG_INFO_OS(g_EngineContext) << "VideoTextureQc " << __FUNCTION__ << " f_camTex: " << f_camTex << " f_eglImage: " << f_eglImage << XLOG_ENDL;

  m_cameraTexturesNearestNeighbor[f_camTex]->setEGLImage(f_eglImage);
}


void VideoTextureQc::updateAndroidTextureId(unsigned int f_camTex, GLuint f_textureId)
{
  if(f_camTex < m_cameraTextures.size())
  {
    m_cameraTextures[f_camTex]->setAndroidTextureID(f_textureId);

    m_cameraTexturesNearestNeighbor[f_camTex]->setAndroidTextureID(f_textureId);
  }
}

osg::StateAttribute* VideoTextureQc::getCameraTexture(unsigned int f_camTex, bool f_useNearestNeighborHint) const
{
  unsigned int l_camTex = f_camTex;
  if (1 == cc::target::sysconf::E_SYSTEM_CONFIG_NUMBER_OF_CAMERAS_TEXTURE)
  {
    if(f_camTex != 0)
    {
        XLOG_INFO(g_EngineContext, "VideoTextureQc using 4x1 texture, but ask to get texture: " << f_camTex );
    }
    l_camTex = 0u;
  }

  assert(l_camTex < m_cameraTextures.size());
  // XLOG_INFO_OS(g_EngineContext) << "VideoTextureQc " << __FUNCTION__ << " l_camTex: " << l_camTex << XLOG_ENDL;

  if (f_useNearestNeighborHint)
  {
    return m_cameraTexturesNearestNeighbor[l_camTex].get();
  }
  return m_cameraTextures[l_camTex].get();
}

} // namespace qualcomm
} // namespace target
} // namespace pc
