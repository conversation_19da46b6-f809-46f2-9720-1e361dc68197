allocations_size: 30200000 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f990840c8: ?? at ??:0
Frame 3: 0x7f99085a7c: ReaderWriterPNG::readImage(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) const at ??:0
Frame 4: 0x7f98feb0f8: osgDB::Registry::ReadImageFunctor::doRead(osgDB::ReaderWriter&) const at ??:0
Frame 5: 0x7f98fe3b00: osgDB::Registry::read(osgDB::Registry::ReadFunctor const&) at ??:0
Frame 6: 0x7f98fe499c: osgDB::Registry::readImplementation(osgDB::Registry::ReadFunctor const&, osgDB::Options::CacheHintOptions) at ??:0
Frame 7: 0x7f98fe5130: osgDB::Registry::readImageImplementation(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) at ??:0

allocations_size: 7979008 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f958acca4: ?? at ??:0
Frame 3: 0x7f958ac998: ?? at ??:0
Frame 4: 0x7f958ac94c: ?? at ??:0
Frame 5: 0x7f94688830: ?? at ??:0
Frame 6: 0x7f9468bef8: ?? at ??:0
Frame 7: 0x7f945d602c: ?? at ??:0

allocations_size: 6764800 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f990840c8: ?? at ??:0
Frame 3: 0x7f99085a7c: ReaderWriterPNG::readImage(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) const at ??:0
Frame 4: 0x7f98feb0f8: osgDB::Registry::ReadImageFunctor::doRead(osgDB::ReaderWriter&) const at ??:0
Frame 5: 0x7f98fe3b00: osgDB::Registry::read(osgDB::Registry::ReadFunctor const&) at ??:0
Frame 6: 0x7f98fe499c: osgDB::Registry::readImplementation(osgDB::Registry::ReadFunctor const&, osgDB::Options::CacheHintOptions) at ??:0
Frame 7: 0x7f98fe5130: osgDB::Registry::readImageImplementation(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) at ??:0

allocations_size: 6291456 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f990840c8: ?? at ??:0
Frame 3: 0x7f99085a7c: ReaderWriterPNG::readImage(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) const at ??:0
Frame 4: 0x7f98feb0f8: osgDB::Registry::ReadImageFunctor::doRead(osgDB::ReaderWriter&) const at ??:0
Frame 5: 0x7f98fe3b00: osgDB::Registry::read(osgDB::Registry::ReadFunctor const&) at ??:0
Frame 6: 0x7f98fe499c: osgDB::Registry::readImplementation(osgDB::Registry::ReadFunctor const&, osgDB::Options::CacheHintOptions) at ??:0
Frame 7: 0x7f98fe5130: osgDB::Registry::readImageImplementation(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) at ??:0

allocations_size: 4718592 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f98e3e38c: osg::Image::allocateImage(int, int, int, unsigned int, unsigned int, int) at ??:0
Frame 3: 0x7f98765100: cc::assets::common::StateSetUpdater::initNewStateSetForCrystal() at ??:0
Frame 4: 0x7f9876c2f4: cc::assets::common::StateSetFinalizer::finalize(pc::vehiclemodel::VehicleModel*) at ??:0
Frame 5: 0x7f98635308: ?? at ??:0
Frame 6: 0x7f9862384c: osg::Group::accept(osg::NodeVisitor&) at ??:0
Frame 7: 0x7f98f93e48: osgViewer::Viewer::updateTraversal() at ??:0

allocations_size: 4718592 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f990840c8: ?? at ??:0
Frame 3: 0x7f99085a7c: ReaderWriterPNG::readImage(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) const at ??:0
Frame 4: 0x7f98feb0f8: osgDB::Registry::ReadImageFunctor::doRead(osgDB::ReaderWriter&) const at ??:0
Frame 5: 0x7f98fe3b00: osgDB::Registry::read(osgDB::Registry::ReadFunctor const&) at ??:0
Frame 6: 0x7f98fe499c: osgDB::Registry::readImplementation(osgDB::Registry::ReadFunctor const&, osgDB::Options::CacheHintOptions) at ??:0
Frame 7: 0x7f98fe5130: osgDB::Registry::readImageImplementation(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) at ??:0

allocations_size: 4718592 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f98e3e38c: osg::Image::allocateImage(int, int, int, unsigned int, unsigned int, int) at ??:0
Frame 3: 0x7f98766cf4: cc::assets::common::StateSetUpdater::initNewStateSetForPBRCarpaint() at ??:0
Frame 4: 0x7f9876c2fc: cc::assets::common::StateSetFinalizer::finalize(pc::vehiclemodel::VehicleModel*) at ??:0
Frame 5: 0x7f98635308: ?? at ??:0
Frame 6: 0x7f9862384c: osg::Group::accept(osg::NodeVisitor&) at ??:0
Frame 7: 0x7f98f93e48: osgViewer::Viewer::updateTraversal() at ??:0

allocations_size: 4718592 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x556b91df14: std::vector<unsigned char, std::allocator<unsigned char>>::_M_fill_insert(__gnu_cxx::__normal_iterator<unsigned char*, std::vector<unsigned char, std::allocator<unsigned char>>>, unsigned long, unsigned char const&) at ??:0
Frame 3: 0x556b91ca24: ?? at ??:0
Frame 4: 0x556b91d32c: ?? at ??:0
Frame 5: 0x556b91d598: ?? at ??:0
Frame 6: 0x556b721a3c: ?? at ??:0
Frame 7: 0x556b721f48: ?? at ??:0

allocations_size: 3145728 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f98e3e38c: osg::Image::allocateImage(int, int, int, unsigned int, unsigned int, int) at ??:0
Frame 3: 0x7f98766b60: cc::assets::common::StateSetUpdater::initNewStateSetForPBRCarpaint() at ??:0
Frame 4: 0x7f9876c2fc: cc::assets::common::StateSetFinalizer::finalize(pc::vehiclemodel::VehicleModel*) at ??:0
Frame 5: 0x7f98635308: ?? at ??:0
Frame 6: 0x7f9862384c: osg::Group::accept(osg::NodeVisitor&) at ??:0
Frame 7: 0x7f98f93e48: osgViewer::Viewer::updateTraversal() at ??:0

allocations_size: 3145728 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f98e3e38c: osg::Image::allocateImage(int, int, int, unsigned int, unsigned int, int) at ??:0
Frame 3: 0x7f98764f6c: cc::assets::common::StateSetUpdater::initNewStateSetForCrystal() at ??:0
Frame 4: 0x7f9876c2f4: cc::assets::common::StateSetFinalizer::finalize(pc::vehiclemodel::VehicleModel*) at ??:0
Frame 5: 0x7f98635308: ?? at ??:0
Frame 6: 0x7f9862384c: osg::Group::accept(osg::NodeVisitor&) at ??:0
Frame 7: 0x7f98f93e48: osgViewer::Viewer::updateTraversal() at ??:0

allocations_size: 3134088 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9468becc: ?? at ??:0
Frame 2: 0x7f945d602c: ?? at ??:0
Frame 3: 0x7f946aacc8: ?? at ??:0
Frame 4: 0x7f9c886488: EGLHelper::initEGL(int, int) at ??:0
Frame 5: 0x7f9c88f13c: ?? at ??:0
Frame 6: 0x7f9c89325c: std::_Function_handler<void (), IntRetMsg::IntRetMsg(std::function<int ()>)::'lambda'()>::_M_invoke(std::_Any_data const&) at ??:0
Frame 7: 0x7f9c895524: MDEventLooper::onRun() at ??:0

allocations_size: 3134088 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9468becc: ?? at ??:0
Frame 2: 0x7f945d602c: ?? at ??:0
Frame 3: 0x7f946aacc8: ?? at ??:0
Frame 4: 0x556b7614e8: ?? at ??:0
Frame 5: 0x556b761600: ?? at ??:0
Frame 6: 0x556b7616a8: ?? at ??:0
Frame 7: 0x556b75accc: ?? at ??:0

allocations_size: 2949120 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95948c8c: ?? at ??:0
Frame 2: 0x7f95946af4: ?? at ??:0
Frame 3: 0x7f959471d8: ?? at ??:0
Frame 4: 0x7f9593f8e4: ?? at ??:0
Frame 5: 0x7f95941030: ?? at ??:0
Frame 6: 0x7f9468bc80: ?? at ??:0
Frame 7: 0x7f958c8dcc: ?? at ??:0

allocations_size: 2523840 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f958b00b0: ?? at ??:0
Frame 3: 0x7f958918f0: ?? at ??:0
Frame 4: 0x7f9589263c: ?? at ??:0
Frame 5: 0x7f958b5388: ?? at ??:0
Frame 6: 0x7f958b55ac: ?? at ??:0
Frame 7: 0x7f958c8a98: ?? at ??:0

allocations_size: 1743360 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f990840c8: ?? at ??:0
Frame 3: 0x7f99085a7c: ReaderWriterPNG::readImage(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) const at ??:0
Frame 4: 0x7f98feb0f8: osgDB::Registry::ReadImageFunctor::doRead(osgDB::ReaderWriter&) const at ??:0
Frame 5: 0x7f98fe3b00: osgDB::Registry::read(osgDB::Registry::ReadFunctor const&) at ??:0
Frame 6: 0x7f98fe499c: osgDB::Registry::readImplementation(osgDB::Registry::ReadFunctor const&, osgDB::Options::CacheHintOptions) at ??:0
Frame 7: 0x7f98fe5130: osgDB::Registry::readImageImplementation(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) at ??:0

allocations_size: 1648624 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f990840c8: ?? at ??:0
Frame 3: 0x7f99085a7c: ReaderWriterPNG::readImage(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) const at ??:0
Frame 4: 0x7f98feb0f8: osgDB::Registry::ReadImageFunctor::doRead(osgDB::ReaderWriter&) const at ??:0
Frame 5: 0x7f98fe3b00: osgDB::Registry::read(osgDB::Registry::ReadFunctor const&) at ??:0
Frame 6: 0x7f98fe499c: osgDB::Registry::readImplementation(osgDB::Registry::ReadFunctor const&, osgDB::Options::CacheHintOptions) at ??:0
Frame 7: 0x7f98fe5130: osgDB::Registry::readImageImplementation(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) at ??:0

allocations_size: 1502496 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9863e604: std::vector<osg::Vec2s, std::allocator<osg::Vec2s>>::_M_fill_insert(__gnu_cxx::__normal_iterator<osg::Vec2s*, std::vector<osg::Vec2s, std::allocator<osg::Vec2s>>>, unsigned long, osg::Vec2s const&) at ??:0
Frame 3: 0x7f9863cccc: pc::assets::BlurredFloorPlate::extractStretchGeometry(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool, std::vector<unsigned short, std::allocator<unsigned short>> const&, std::vector<unsigned char, std::allocator<unsigned char>> const&, osg::Vec2f const&, osg::Vec2f const&) at ??:0
Frame 4: 0x7f98642e70: pc::assets::NoCamBorderUpdateVisitor::createBaseplateAndTextureStretchGeometry(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool, std::vector<unsigned short, std::allocator<unsigned short>> const&) at ??:0
Frame 5: 0x7f986448d4: pc::assets::NoCamBorderUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 6: 0x7f9869d450: pc::factory::SV3DUpdateVisitor::apply(osg::Group&) at ??:0
Frame 7: 0x7f98640228: pc::assets::FloorPlateRenderer::FloorPlateRenderer(pc::factory::Floor*, pc::worker::bowlshaping::PolarBowlLayout const&, pc::texfloor::core::FloorGenerator*, pc::assets::FloorPlateStateHandler*, cc::core::AssetId, pc::core::Framework*, bool) at ??:0

allocations_size: 1080000 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f990840c8: ?? at ??:0
Frame 3: 0x7f99085a7c: ReaderWriterPNG::readImage(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) const at ??:0
Frame 4: 0x7f98feb0f8: osgDB::Registry::ReadImageFunctor::doRead(osgDB::ReaderWriter&) const at ??:0
Frame 5: 0x7f98fe3b00: osgDB::Registry::read(osgDB::Registry::ReadFunctor const&) at ??:0
Frame 6: 0x7f98fe499c: osgDB::Registry::readImplementation(osgDB::Registry::ReadFunctor const&, osgDB::Options::CacheHintOptions) at ??:0
Frame 7: 0x7f98fe5130: osgDB::Registry::readImageImplementation(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) at ??:0

allocations_size: 1023224 B
Frame 0: 0x7f9cd92588: calloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f945d6004: ?? at ??:0
Frame 2: 0x7f946aacc8: ?? at ??:0
Frame 3: 0x556b7614e8: ?? at ??:0
Frame 4: 0x556b761600: ?? at ??:0
Frame 5: 0x556b7616a8: ?? at ??:0
Frame 6: 0x556b75accc: ?? at ??:0
Frame 7: 0x556b758d08: ?? at ??:0

allocations_size: 1023224 B
Frame 0: 0x7f9cd92588: calloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f945d6004: ?? at ??:0
Frame 2: 0x7f946aacc8: ?? at ??:0
Frame 3: 0x7f9c886488: EGLHelper::initEGL(int, int) at ??:0
Frame 4: 0x7f9c88f13c: ?? at ??:0
Frame 5: 0x7f9c89325c: std::_Function_handler<void (), IntRetMsg::IntRetMsg(std::function<int ()>)::'lambda'()>::_M_invoke(std::_Any_data const&) at ??:0
Frame 6: 0x7f9c895524: MDEventLooper::onRun() at ??:0
Frame 7: 0x7f9c89572c: MDEventLooper::runLinux(void*) at ??:0

allocations_size: 1021440 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f9468eaf0: ?? at ??:0
Frame 3: 0x7f94688788: ?? at ??:0
Frame 4: 0x7f9468bef8: ?? at ??:0
Frame 5: 0x7f945d602c: ?? at ??:0
Frame 6: 0x7f946aacc8: ?? at ??:0
Frame 7: 0x556b7614e8: ?? at ??:0

allocations_size: 1021440 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f9468eaf0: ?? at ??:0
Frame 3: 0x7f94688788: ?? at ??:0
Frame 4: 0x7f9468bef8: ?? at ??:0
Frame 5: 0x7f945d602c: ?? at ??:0
Frame 6: 0x7f946aacc8: ?? at ??:0
Frame 7: 0x7f9c886488: EGLHelper::initEGL(int, int) at ??:0

allocations_size: 998784 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f986ea9c8: pc::util::osgx::ProjectionObjects::createProjectionPlane(osg::Vec3f const&, osg::Vec3f const&, osg::Vec3f const&, unsigned int, unsigned int) at ??:0
Frame 3: 0x7f986eb228: pc::util::osgx::ProjectionObjects::addWall(osg::Vec3f const&, osg::Vec3f const&, osg::Vec3f const&, unsigned int, unsigned int) at ??:0
Frame 4: 0x7f9870c08c: pc::views::warpfisheye::WarpFisheyeView::updateViewportBounds(float, float, float, float) at ??:0
Frame 5: 0x7f9870c964: pc::views::warpfisheye::WarpFisheyeView::createView(osg::Vec4f) at ??:0
Frame 6: 0x7f9870cdc4: pc::views::warpfisheye::WarpFisheyeView::WarpFisheyeView(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, pc::core::Viewport const&, pc::core::Framework*, pc::core::sysconf::Cameras, pc::views::warpfisheye::FisheyeModel*, pc::views::warpfisheye::FisheyeViewSettings const*, osg::Vec4f, rbp::vis::imp::sh::ESharpnessView, rbp::vis::imp::tnf::ETnfView) at ??:0
Frame 7: 0x7f98b96d2c: cc::views::warpfisheye::CustomWarpFisheyeView::CustomWarpFisheyeView(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, pc::core::Viewport const&, pc::core::Framework*, pc::core::sysconf::Cameras, pc::views::warpfisheye::FisheyeModel*, pc::views::warpfisheye::FisheyeViewSettings const*, cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings const*, rbp::vis::imp::sh::ESharpnessView, rbp::vis::imp::tnf::ETnfView) at ??:0

allocations_size: 960000 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f986f8950: pc::util::osgx::createDrawElements(unsigned int, unsigned int, unsigned int) at ??:0
Frame 3: 0x7f986eac50: pc::util::osgx::ProjectionObjects::createProjectionPlane(osg::Vec3f const&, osg::Vec3f const&, osg::Vec3f const&, unsigned int, unsigned int) at ??:0
Frame 4: 0x7f986eb228: pc::util::osgx::ProjectionObjects::addWall(osg::Vec3f const&, osg::Vec3f const&, osg::Vec3f const&, unsigned int, unsigned int) at ??:0
Frame 5: 0x7f9870c08c: pc::views::warpfisheye::WarpFisheyeView::updateViewportBounds(float, float, float, float) at ??:0
Frame 6: 0x7f9870c964: pc::views::warpfisheye::WarpFisheyeView::createView(osg::Vec4f) at ??:0
Frame 7: 0x7f9870cdc4: pc::views::warpfisheye::WarpFisheyeView::WarpFisheyeView(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, pc::core::Viewport const&, pc::core::Framework*, pc::core::sysconf::Cameras, pc::views::warpfisheye::FisheyeModel*, pc::views::warpfisheye::FisheyeViewSettings const*, osg::Vec4f, rbp::vis::imp::sh::ESharpnessView, rbp::vis::imp::tnf::ETnfView) at ??:0

allocations_size: 916160 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f95c9a414: ?? at ??:0
Frame 3: 0x7f95c069c0: ?? at ??:0
Frame 4: 0x7f95c08d14: ?? at ??:0
Frame 5: 0x7f95957824: ?? at ??:0
Frame 6: 0x7f9595bd60: ?? at ??:0
Frame 7: 0x7f959803a8: ?? at ??:0

allocations_size: 909312 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95948c8c: ?? at ??:0
Frame 2: 0x7f95946af4: ?? at ??:0
Frame 3: 0x7f959471d8: ?? at ??:0
Frame 4: 0x7f959444ec: ?? at ??:0
Frame 5: 0x7f9593f8b4: ?? at ??:0
Frame 6: 0x7f95942174: ?? at ??:0
Frame 7: 0x7f958988f4: ?? at ??:0

allocations_size: 738176 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f990840c8: ?? at ??:0
Frame 3: 0x7f99085a7c: ReaderWriterPNG::readImage(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) const at ??:0
Frame 4: 0x7f98feb0f8: osgDB::Registry::ReadImageFunctor::doRead(osgDB::ReaderWriter&) const at ??:0
Frame 5: 0x7f98fe3b00: osgDB::Registry::read(osgDB::Registry::ReadFunctor const&) at ??:0
Frame 6: 0x7f98fe499c: osgDB::Registry::readImplementation(osgDB::Registry::ReadFunctor const&, osgDB::Options::CacheHintOptions) at ??:0
Frame 7: 0x7f98fe5130: osgDB::Registry::readImageImplementation(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) at ??:0

allocations_size: 665856 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f986eab08: pc::util::osgx::ProjectionObjects::createProjectionPlane(osg::Vec3f const&, osg::Vec3f const&, osg::Vec3f const&, unsigned int, unsigned int) at ??:0
Frame 3: 0x7f986eb228: pc::util::osgx::ProjectionObjects::addWall(osg::Vec3f const&, osg::Vec3f const&, osg::Vec3f const&, unsigned int, unsigned int) at ??:0
Frame 4: 0x7f9870c08c: pc::views::warpfisheye::WarpFisheyeView::updateViewportBounds(float, float, float, float) at ??:0
Frame 5: 0x7f9870c964: pc::views::warpfisheye::WarpFisheyeView::createView(osg::Vec4f) at ??:0
Frame 6: 0x7f9870cdc4: pc::views::warpfisheye::WarpFisheyeView::WarpFisheyeView(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, pc::core::Viewport const&, pc::core::Framework*, pc::core::sysconf::Cameras, pc::views::warpfisheye::FisheyeModel*, pc::views::warpfisheye::FisheyeViewSettings const*, osg::Vec4f, rbp::vis::imp::sh::ESharpnessView, rbp::vis::imp::tnf::ETnfView) at ??:0
Frame 7: 0x7f98b96d2c: cc::views::warpfisheye::CustomWarpFisheyeView::CustomWarpFisheyeView(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, pc::core::Viewport const&, pc::core::Framework*, pc::core::sysconf::Cameras, pc::views::warpfisheye::FisheyeModel*, pc::views::warpfisheye::FisheyeViewSettings const*, cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings const*, rbp::vis::imp::sh::ESharpnessView, rbp::vis::imp::tnf::ETnfView) at ??:0

allocations_size: 655360 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95948c8c: ?? at ??:0
Frame 2: 0x7f95946af4: ?? at ??:0
Frame 3: 0x7f959471d8: ?? at ??:0
Frame 4: 0x7f959444ec: ?? at ??:0
Frame 5: 0x7f959409dc: ?? at ??:0
Frame 6: 0x7f94668444: ?? at ??:0
Frame 7: 0x7f9466d314: ?? at ??:0

allocations_size: 563436 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f98624db0: std::vector<osg::Vec3f, std::allocator<osg::Vec3f>>::_M_fill_insert(__gnu_cxx::__normal_iterator<osg::Vec3f*, std::vector<osg::Vec3f, std::allocator<osg::Vec3f>>>, unsigned long, osg::Vec3f const&) at ??:0
Frame 3: 0x7f9869e5d4: pc::factory::SV3DUpdateVisitor::resizeArrays(unsigned int) at ??:0
Frame 4: 0x7f9869e658: pc::factory::SV3DUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 5: 0x7f986804c8: pc::factory::FloorUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 6: 0x7f986444b8: pc::assets::NoCamBorderUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 7: 0x7f9869d450: pc::factory::SV3DUpdateVisitor::apply(osg::Group&) at ??:0

allocations_size: 563436 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f98624db0: std::vector<osg::Vec3f, std::allocator<osg::Vec3f>>::_M_fill_insert(__gnu_cxx::__normal_iterator<osg::Vec3f*, std::vector<osg::Vec3f, std::allocator<osg::Vec3f>>>, unsigned long, osg::Vec3f const&) at ??:0
Frame 3: 0x7f9869e5e4: pc::factory::SV3DUpdateVisitor::resizeArrays(unsigned int) at ??:0
Frame 4: 0x7f9869e658: pc::factory::SV3DUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 5: 0x7f986804c8: pc::factory::FloorUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 6: 0x7f986444b8: pc::assets::NoCamBorderUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 7: 0x7f9869d450: pc::factory::SV3DUpdateVisitor::apply(osg::Group&) at ??:0

allocations_size: 563436 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f98624db0: std::vector<osg::Vec3f, std::allocator<osg::Vec3f>>::_M_fill_insert(__gnu_cxx::__normal_iterator<osg::Vec3f*, std::vector<osg::Vec3f, std::allocator<osg::Vec3f>>>, unsigned long, osg::Vec3f const&) at ??:0
Frame 3: 0x7f9869e5b4: pc::factory::SV3DUpdateVisitor::resizeArrays(unsigned int) at ??:0
Frame 4: 0x7f9869e658: pc::factory::SV3DUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 5: 0x7f986804c8: pc::factory::FloorUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 6: 0x7f986444b8: pc::assets::NoCamBorderUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 7: 0x7f9869d450: pc::factory::SV3DUpdateVisitor::apply(osg::Group&) at ??:0

allocations_size: 563436 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f98624db0: std::vector<osg::Vec3f, std::allocator<osg::Vec3f>>::_M_fill_insert(__gnu_cxx::__normal_iterator<osg::Vec3f*, std::vector<osg::Vec3f, std::allocator<osg::Vec3f>>>, unsigned long, osg::Vec3f const&) at ??:0
Frame 3: 0x7f9869e5c4: pc::factory::SV3DUpdateVisitor::resizeArrays(unsigned int) at ??:0
Frame 4: 0x7f9869e658: pc::factory::SV3DUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 5: 0x7f986804c8: pc::factory::FloorUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 6: 0x7f986444b8: pc::assets::NoCamBorderUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 7: 0x7f9869d450: pc::factory::SV3DUpdateVisitor::apply(osg::Group&) at ??:0

allocations_size: 554400 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9867e998: pc::factory::Floor::build() at ??:0
Frame 3: 0x7f986804a4: pc::factory::FloorUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 4: 0x7f986444b8: pc::assets::NoCamBorderUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 5: 0x7f9869d450: pc::factory::SV3DUpdateVisitor::apply(osg::Group&) at ??:0
Frame 6: 0x7f98640228: pc::assets::FloorPlateRenderer::FloorPlateRenderer(pc::factory::Floor*, pc::worker::bowlshaping::PolarBowlLayout const&, pc::texfloor::core::FloorGenerator*, pc::assets::FloorPlateStateHandler*, cc::core::AssetId, pc::core::Framework*, bool) at ??:0
Frame 7: 0x7f98782c44: cc::assets::common::CustomFloorPlateRenderer::CustomFloorPlateRenderer(pc::factory::Floor*, pc::worker::bowlshaping::PolarBowlLayout const&, pc::texfloor::core::FloorGenerator*, cc::core::AssetId, pc::core::Framework*, bool) at ??:0

allocations_size: 529920 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f990840c8: ?? at ??:0
Frame 3: 0x7f99085a7c: ReaderWriterPNG::readImage(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) const at ??:0
Frame 4: 0x7f98feb0f8: osgDB::Registry::ReadImageFunctor::doRead(osgDB::ReaderWriter&) const at ??:0
Frame 5: 0x7f98fe3b00: osgDB::Registry::read(osgDB::Registry::ReadFunctor const&) at ??:0
Frame 6: 0x7f98fe499c: osgDB::Registry::readImplementation(osgDB::Registry::ReadFunctor const&, osgDB::Options::CacheHintOptions) at ??:0
Frame 7: 0x7f98fe5130: osgDB::Registry::readImageImplementation(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) at ??:0

allocations_size: 524288 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95948c8c: ?? at ??:0
Frame 2: 0x7f95946af4: ?? at ??:0
Frame 3: 0x7f959471d8: ?? at ??:0
Frame 4: 0x7f959444ec: ?? at ??:0
Frame 5: 0x7f959409dc: ?? at ??:0
Frame 6: 0x7f945d1b20: ?? at ??:0
Frame 7: 0x7f945d1334: ?? at ??:0

allocations_size: 504360 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f96550a34: ?? at ??:0
Frame 2: 0x7f9638a898: ?? at ??:0
Frame 3: 0x7f963dc8b8: ?? at ??:0
Frame 4: 0x7f96002b38: ?? at ??:0
Frame 5: 0x7f96002050: ?? at ??:0
Frame 6: 0x7f95ffe780: ?? at ??:0
Frame 7: 0x7f959579f4: ?? at ??:0

allocations_size: 440448 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f990840c8: ?? at ??:0
Frame 3: 0x7f99085a7c: ReaderWriterPNG::readImage(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) const at ??:0
Frame 4: 0x7f98feb0f8: osgDB::Registry::ReadImageFunctor::doRead(osgDB::ReaderWriter&) const at ??:0
Frame 5: 0x7f98fe3b00: osgDB::Registry::read(osgDB::Registry::ReadFunctor const&) at ??:0
Frame 6: 0x7f98fe499c: osgDB::Registry::readImplementation(osgDB::Registry::ReadFunctor const&, osgDB::Options::CacheHintOptions) at ??:0
Frame 7: 0x7f98fe5130: osgDB::Registry::readImageImplementation(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) at ??:0

allocations_size: 434176 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95948c8c: ?? at ??:0
Frame 2: 0x7f95946af4: ?? at ??:0
Frame 3: 0x7f959471d8: ?? at ??:0
Frame 4: 0x7f9594453c: ?? at ??:0
Frame 5: 0x7f9593f8b4: ?? at ??:0
Frame 6: 0x7f95942174: ?? at ??:0
Frame 7: 0x7f958988f4: ?? at ??:0

allocations_size: 428800 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f990840c8: ?? at ??:0
Frame 3: 0x7f99085a7c: ReaderWriterPNG::readImage(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) const at ??:0
Frame 4: 0x7f98feb0f8: osgDB::Registry::ReadImageFunctor::doRead(osgDB::ReaderWriter&) const at ??:0
Frame 5: 0x7f98fe3b00: osgDB::Registry::read(osgDB::Registry::ReadFunctor const&) at ??:0
Frame 6: 0x7f98fe499c: osgDB::Registry::readImplementation(osgDB::Registry::ReadFunctor const&, osgDB::Options::CacheHintOptions) at ??:0
Frame 7: 0x7f98fe5130: osgDB::Registry::readImageImplementation(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) at ??:0

allocations_size: 411000 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9638a7c8: ?? at ??:0
Frame 3: 0x7f963dc8b8: ?? at ??:0
Frame 4: 0x7f96002b38: ?? at ??:0
Frame 5: 0x7f96002050: ?? at ??:0
Frame 6: 0x7f95ffe780: ?? at ??:0
Frame 7: 0x7f959579f4: ?? at ??:0

allocations_size: 393216 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9863e36c: void std::vector<unsigned short, std::allocator<unsigned short>>::_M_realloc_insert<unsigned short const&>(__gnu_cxx::__normal_iterator<unsigned short*, std::vector<unsigned short, std::allocator<unsigned short>>>, unsigned short const&) at ??:0
Frame 3: 0x7f9869e244: pc::factory::SV3DUpdateVisitor::assignVertices(pc::factory::SV3DNode*, std::vector<unsigned char, std::allocator<unsigned char>> const&) at ??:0
Frame 4: 0x7f9869e6d0: pc::factory::SV3DUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 5: 0x7f986804c8: pc::factory::FloorUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 6: 0x7f986444b8: pc::assets::NoCamBorderUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 7: 0x7f9869d450: pc::factory::SV3DUpdateVisitor::apply(osg::Group&) at ??:0

allocations_size: 380512 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f990840c8: ?? at ??:0
Frame 3: 0x7f99085a7c: ReaderWriterPNG::readImage(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) const at ??:0
Frame 4: 0x7f98feb0f8: osgDB::Registry::ReadImageFunctor::doRead(osgDB::ReaderWriter&) const at ??:0
Frame 5: 0x7f98fe3b00: osgDB::Registry::read(osgDB::Registry::ReadFunctor const&) at ??:0
Frame 6: 0x7f98fe499c: osgDB::Registry::readImplementation(osgDB::Registry::ReadFunctor const&, osgDB::Options::CacheHintOptions) at ??:0
Frame 7: 0x7f98fe5130: osgDB::Registry::readImageImplementation(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) at ??:0

allocations_size: 377232 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9863d3e4: pc::assets::BlurredFloorPlate::extractStretchGeometry(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool, std::vector<unsigned short, std::allocator<unsigned short>> const&, std::vector<unsigned char, std::allocator<unsigned char>> const&, osg::Vec2f const&, osg::Vec2f const&) at ??:0
Frame 3: 0x7f98642e70: pc::assets::NoCamBorderUpdateVisitor::createBaseplateAndTextureStretchGeometry(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool, std::vector<unsigned short, std::allocator<unsigned short>> const&) at ??:0
Frame 4: 0x7f986448d4: pc::assets::NoCamBorderUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 5: 0x7f9869d450: pc::factory::SV3DUpdateVisitor::apply(osg::Group&) at ??:0
Frame 6: 0x7f98640228: pc::assets::FloorPlateRenderer::FloorPlateRenderer(pc::factory::Floor*, pc::worker::bowlshaping::PolarBowlLayout const&, pc::texfloor::core::FloorGenerator*, pc::assets::FloorPlateStateHandler*, cc::core::AssetId, pc::core::Framework*, bool) at ??:0
Frame 7: 0x7f98782c44: cc::assets::common::CustomFloorPlateRenderer::CustomFloorPlateRenderer(pc::factory::Floor*, pc::worker::bowlshaping::PolarBowlLayout const&, pc::texfloor::core::FloorGenerator*, cc::core::AssetId, pc::core::Framework*, bool) at ??:0

allocations_size: 375624 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x556b91df14: std::vector<unsigned char, std::allocator<unsigned char>>::_M_fill_insert(__gnu_cxx::__normal_iterator<unsigned char*, std::vector<unsigned char, std::allocator<unsigned char>>>, unsigned long, unsigned char const&) at ??:0
Frame 3: 0x7f9863cc90: pc::assets::BlurredFloorPlate::extractStretchGeometry(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool, std::vector<unsigned short, std::allocator<unsigned short>> const&, std::vector<unsigned char, std::allocator<unsigned char>> const&, osg::Vec2f const&, osg::Vec2f const&) at ??:0
Frame 4: 0x7f98642e70: pc::assets::NoCamBorderUpdateVisitor::createBaseplateAndTextureStretchGeometry(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool, std::vector<unsigned short, std::allocator<unsigned short>> const&) at ??:0
Frame 5: 0x7f986448d4: pc::assets::NoCamBorderUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 6: 0x7f9869d450: pc::factory::SV3DUpdateVisitor::apply(osg::Group&) at ??:0
Frame 7: 0x7f98640228: pc::assets::FloorPlateRenderer::FloorPlateRenderer(pc::factory::Floor*, pc::worker::bowlshaping::PolarBowlLayout const&, pc::texfloor::core::FloorGenerator*, pc::assets::FloorPlateStateHandler*, cc::core::AssetId, pc::core::Framework*, bool) at ??:0

allocations_size: 360448 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95948c8c: ?? at ??:0
Frame 2: 0x7f95946af4: ?? at ??:0
Frame 3: 0x7f959471d8: ?? at ??:0
Frame 4: 0x7f959444ec: ?? at ??:0
Frame 5: 0x7f9593f8b4: ?? at ??:0
Frame 6: 0x7f95941030: ?? at ??:0
Frame 7: 0x7f9468bc80: ?? at ??:0

allocations_size: 353280 B
Frame 0: 0x7f9cd92cf8: aligned_alloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:975
Frame 1: 0x7f9aee8900: operator new(unsigned long, std::align_val_t) at ??:0
Frame 2: 0x7f95b7b49c: ?? at ??:0
Frame 3: 0x7f95b7b420: ?? at ??:0
Frame 4: 0x7f95b7a3a8: ?? at ??:0
Frame 5: 0x7f95b77cf0: ?? at ??:0
Frame 6: 0x7f95b7504c: ?? at ??:0
Frame 7: 0x7f963dc8b8: ?? at ??:0

allocations_size: 338520 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f990840c8: ?? at ??:0
Frame 3: 0x7f99085a7c: ReaderWriterPNG::readImage(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) const at ??:0
Frame 4: 0x7f98feb0f8: osgDB::Registry::ReadImageFunctor::doRead(osgDB::ReaderWriter&) const at ??:0
Frame 5: 0x7f98fe3b00: osgDB::Registry::read(osgDB::Registry::ReadFunctor const&) at ??:0
Frame 6: 0x7f98fe499c: osgDB::Registry::readImplementation(osgDB::Registry::ReadFunctor const&, osgDB::Options::CacheHintOptions) at ??:0
Frame 7: 0x7f98fe5130: osgDB::Registry::readImageImplementation(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) at ??:0

allocations_size: 318200 B
Frame 0: 0x7f9cd92588: calloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9b482834: g_malloc0 at ??:0
Frame 2: 0x7f9b2e7c90: gst_structure_new_id at ??:0
Frame 3: 0x7f9b2a16b0: gst_event_new_caps at ??:0
Frame 4: 0x7f93b4bb5c: ?? at ??:0
Frame 5: 0x7f93b49f04: ?? at ??:0
Frame 6: 0x7f4c4ace1c: ?? at ??:0
Frame 7: 0x7f9b2c2bd4: ?? at ??:0

allocations_size: 318200 B
Frame 0: 0x7f9cd92588: calloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9b482834: g_malloc0 at ??:0
Frame 2: 0x7f9b2ec7b4: gst_structure_intersect at ??:0
Frame 3: 0x7f9b286d84: ?? at ??:0
Frame 4: 0x7f4c4ac5b4: ?? at ??:0
Frame 5: 0x7f93b4aebc: ?? at ??:0
Frame 6: 0x7f93b48e50: ?? at ??:0
Frame 7: 0x7f9b2be7b4: gst_pad_query at ??:0

allocations_size: 318200 B
Frame 0: 0x7f9cd92588: calloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9b482834: g_malloc0 at ??:0
Frame 2: 0x7f9b2e7c90: gst_structure_new_id at ??:0
Frame 3: 0x7f9b2a2fa4: gst_event_new_stream_start at ??:0
Frame 4: 0x7f4c7499bc: ?? at ??:0
Frame 5: 0x7f9b29b664: gst_element_change_state at ??:0
Frame 6: 0x7f9b29cce0: ?? at ??:0
Frame 7: 0x7f9b271860: ?? at ??:0

allocations_size: 318200 B
Frame 0: 0x7f9cd92588: calloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9b482834: g_malloc0 at ??:0
Frame 2: 0x7f9b2e7c90: gst_structure_new_id at ??:0
Frame 3: 0x7f9b2a16b0: gst_event_new_caps at ??:0
Frame 4: 0x7f9b1fd1f0: ?? at ??:0
Frame 5: 0x7f93b35a94: ?? at ??:0
Frame 6: 0x7f9b1fcc8c: ?? at ??:0
Frame 7: 0x7f93b370f0: ?? at ??:0

allocations_size: 302712 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9aee884c: operator new(unsigned long, std::nothrow_t const&) at ??:0
Frame 3: 0x7f94687408: ?? at ??:0
Frame 4: 0x7f9468ad4c: ?? at ??:0
Frame 5: 0x7f94681144: ?? at ??:0
Frame 6: 0x7f94681af4: ?? at ??:0
Frame 7: 0x7f94681bf0: ?? at ??:0

allocations_size: 278528 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95948c8c: ?? at ??:0
Frame 2: 0x7f95946af4: ?? at ??:0
Frame 3: 0x7f959471d8: ?? at ??:0
Frame 4: 0x7f959444ec: ?? at ??:0
Frame 5: 0x7f95943674: ?? at ??:0
Frame 6: 0x7f95943478: ?? at ??:0
Frame 7: 0x7f9594034c: ?? at ??:0

allocations_size: 262144 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9b671390: ?? at ??:0
Frame 3: 0x7f9b67117c: ?? at ??:0
Frame 4: 0x7f9b670f1c: ?? at ??:0
Frame 5: 0x7f9b66f2bc: ?? at ??:0
Frame 6: 0x7f9b6787e4: ?? at ??:0
Frame 7: 0x7f9b66bd38: ?? at ??:0

allocations_size: 262144 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9b9f1468: ?? at ??:0
Frame 3: 0x7f9b9f127c: ?? at ??:0
Frame 4: 0x7f9b9f1018: ?? at ??:0
Frame 5: 0x7f9b9ef3b8: ?? at ??:0
Frame 6: 0x7f9b9f88e4: ?? at ??:0
Frame 7: 0x7f9b9ebe34: ?? at ??:0

allocations_size: 262144 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9b671368: ?? at ??:0
Frame 3: 0x7f9b67117c: ?? at ??:0
Frame 4: 0x7f9b670f1c: ?? at ??:0
Frame 5: 0x7f9b66f2bc: ?? at ??:0
Frame 6: 0x7f9b6787e4: ?? at ??:0
Frame 7: 0x7f9b66bd38: ?? at ??:0

allocations_size: 262144 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9bd98458: ?? at ??:0
Frame 3: 0x7f9bd98244: ?? at ??:0
Frame 4: 0x7f9bd97fe4: ?? at ??:0
Frame 5: 0x7f9bd96584: ?? at ??:0
Frame 6: 0x7f9bd9f600: ?? at ??:0
Frame 7: 0x7f9bd93880: ?? at ??:0

allocations_size: 262144 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9bd98430: ?? at ??:0
Frame 3: 0x7f9bd98244: ?? at ??:0
Frame 4: 0x7f9bd97fe4: ?? at ??:0
Frame 5: 0x7f9bd96584: ?? at ??:0
Frame 6: 0x7f9bd9f600: ?? at ??:0
Frame 7: 0x7f9bd93880: ?? at ??:0

allocations_size: 262144 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9c13fd80: ?? at ??:0
Frame 3: 0x7f9c13fb6c: ?? at ??:0
Frame 4: 0x7f9c13f90c: ?? at ??:0
Frame 5: 0x7f9c13deac: ?? at ??:0
Frame 6: 0x7f9c146f28: ?? at ??:0
Frame 7: 0x7f9c13b1a8: ?? at ??:0

allocations_size: 262144 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95950a40: ?? at ??:0
Frame 2: 0x7f94615fec: ?? at ??:0
Frame 3: 0x7f945d6198: ?? at ??:0
Frame 4: 0x7f946aacc8: ?? at ??:0
Frame 5: 0x556b7614e8: ?? at ??:0
Frame 6: 0x556b761600: ?? at ??:0
Frame 7: 0x556b7616a8: ?? at ??:0

allocations_size: 262144 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9863e36c: void std::vector<unsigned short, std::allocator<unsigned short>>::_M_realloc_insert<unsigned short const&>(__gnu_cxx::__normal_iterator<unsigned short*, std::vector<unsigned short, std::allocator<unsigned short>>>, unsigned short const&) at ??:0
Frame 3: 0x7f98fff030: osgDB::InputStream::readPrimitiveSet() at ??:0
Frame 4: 0x7f9918d41c: osgDB::ListSerializer<osg::Geometry, std::vector<osg::ref_ptr<osg::PrimitiveSet>, std::allocator<osg::ref_ptr<osg::PrimitiveSet>>>>::read(osgDB::InputStream&, osg::Object&) at ??:0
Frame 5: 0x7f98ff3608: osgDB::ObjectWrapper::read(osgDB::InputStream&, osg::Object&) at ??:0
Frame 6: 0x7f98ffd5a4: osgDB::InputStream::readObjectFields(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, unsigned int, osg::Object*) at ??:0
Frame 7: 0x7f98ffe6ac: osgDB::InputStream::readObject(osg::Object*) at ??:0

allocations_size: 262144 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9c13fd58: ?? at ??:0
Frame 3: 0x7f9c13fb6c: ?? at ??:0
Frame 4: 0x7f9c13f90c: ?? at ??:0
Frame 5: 0x7f9c13deac: ?? at ??:0
Frame 6: 0x7f9c146f28: ?? at ??:0
Frame 7: 0x7f9c13b1a8: ?? at ??:0

allocations_size: 262144 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95950a40: ?? at ??:0
Frame 2: 0x7f94615fec: ?? at ??:0
Frame 3: 0x7f945d6198: ?? at ??:0
Frame 4: 0x7f946aacc8: ?? at ??:0
Frame 5: 0x7f9c886488: EGLHelper::initEGL(int, int) at ??:0
Frame 6: 0x7f9c88f13c: ?? at ??:0
Frame 7: 0x7f9c89325c: std::_Function_handler<void (), IntRetMsg::IntRetMsg(std::function<int ()>)::'lambda'()>::_M_invoke(std::_Any_data const&) at ??:0

allocations_size: 262144 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9b9f1490: ?? at ??:0
Frame 3: 0x7f9b9f127c: ?? at ??:0
Frame 4: 0x7f9b9f1018: ?? at ??:0
Frame 5: 0x7f9b9ef3b8: ?? at ??:0
Frame 6: 0x7f9b9f88e4: ?? at ??:0
Frame 7: 0x7f9b9ebe34: ?? at ??:0

allocations_size: 231264 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f945d1bf0: ?? at ??:0
Frame 2: 0x7f95b09274: ?? at ??:0
Frame 3: 0x7f945d19c8: ?? at ??:0
Frame 4: 0x7f945d1af0: ?? at ??:0
Frame 5: 0x7f945cfa50: ?? at ??:0
Frame 6: 0x7f946046bc: ?? at ??:0
Frame 7: 0x7f98dee6d4: osg::GLBufferObject::compileBuffer() at ??:0

allocations_size: 225280 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f95891da8: ?? at ??:0
Frame 3: 0x7f9589263c: ?? at ??:0
Frame 4: 0x7f958b5388: ?? at ??:0
Frame 5: 0x7f958b55ac: ?? at ??:0
Frame 6: 0x7f958c8a98: ?? at ??:0
Frame 7: 0x7f958b6994: ?? at ??:0

allocations_size: 222608 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f9468c810: ?? at ??:0
Frame 3: 0x7f9468c9f4: ?? at ??:0
Frame 4: 0x7f9468879c: ?? at ??:0
Frame 5: 0x7f9468bef8: ?? at ??:0
Frame 6: 0x7f945d602c: ?? at ??:0
Frame 7: 0x7f946aacc8: ?? at ??:0

allocations_size: 222608 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f9468c810: ?? at ??:0
Frame 3: 0x7f9468c9c4: ?? at ??:0
Frame 4: 0x7f9468879c: ?? at ??:0
Frame 5: 0x7f9468bef8: ?? at ??:0
Frame 6: 0x7f945d602c: ?? at ??:0
Frame 7: 0x7f946aacc8: ?? at ??:0

allocations_size: 222608 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f9468c810: ?? at ??:0
Frame 3: 0x7f9468c994: ?? at ??:0
Frame 4: 0x7f9468879c: ?? at ??:0
Frame 5: 0x7f9468bef8: ?? at ??:0
Frame 6: 0x7f945d602c: ?? at ??:0
Frame 7: 0x7f946aacc8: ?? at ??:0

allocations_size: 222608 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f9468c810: ?? at ??:0
Frame 3: 0x7f9468ca18: ?? at ??:0
Frame 4: 0x7f9468879c: ?? at ??:0
Frame 5: 0x7f9468bef8: ?? at ??:0
Frame 6: 0x7f945d602c: ?? at ??:0
Frame 7: 0x7f946aacc8: ?? at ??:0

allocations_size: 222608 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f9468c810: ?? at ??:0
Frame 3: 0x7f9468c9e8: ?? at ??:0
Frame 4: 0x7f9468879c: ?? at ??:0
Frame 5: 0x7f9468bef8: ?? at ??:0
Frame 6: 0x7f945d602c: ?? at ??:0
Frame 7: 0x7f946aacc8: ?? at ??:0

allocations_size: 222608 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f9468c810: ?? at ??:0
Frame 3: 0x7f9468c9b8: ?? at ??:0
Frame 4: 0x7f9468879c: ?? at ??:0
Frame 5: 0x7f9468bef8: ?? at ??:0
Frame 6: 0x7f945d602c: ?? at ??:0
Frame 7: 0x7f946aacc8: ?? at ??:0

allocations_size: 222608 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f9468c810: ?? at ??:0
Frame 3: 0x7f9468c988: ?? at ??:0
Frame 4: 0x7f9468879c: ?? at ??:0
Frame 5: 0x7f9468bef8: ?? at ??:0
Frame 6: 0x7f945d602c: ?? at ??:0
Frame 7: 0x7f946aacc8: ?? at ??:0

allocations_size: 222608 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f9468c810: ?? at ??:0
Frame 3: 0x7f9468ca0c: ?? at ??:0
Frame 4: 0x7f9468879c: ?? at ??:0
Frame 5: 0x7f9468bef8: ?? at ??:0
Frame 6: 0x7f945d602c: ?? at ??:0
Frame 7: 0x7f946aacc8: ?? at ??:0

allocations_size: 222608 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f9468c810: ?? at ??:0
Frame 3: 0x7f9468c9dc: ?? at ??:0
Frame 4: 0x7f9468879c: ?? at ??:0
Frame 5: 0x7f9468bef8: ?? at ??:0
Frame 6: 0x7f945d602c: ?? at ??:0
Frame 7: 0x7f946aacc8: ?? at ??:0

allocations_size: 222608 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f9468c810: ?? at ??:0
Frame 3: 0x7f9468c9ac: ?? at ??:0
Frame 4: 0x7f9468879c: ?? at ??:0
Frame 5: 0x7f9468bef8: ?? at ??:0
Frame 6: 0x7f945d602c: ?? at ??:0
Frame 7: 0x7f946aacc8: ?? at ??:0

allocations_size: 222608 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f9468c810: ?? at ??:0
Frame 3: 0x7f9468c97c: ?? at ??:0
Frame 4: 0x7f9468879c: ?? at ??:0
Frame 5: 0x7f9468bef8: ?? at ??:0
Frame 6: 0x7f945d602c: ?? at ??:0
Frame 7: 0x7f946aacc8: ?? at ??:0

allocations_size: 222608 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f9468c810: ?? at ??:0
Frame 3: 0x7f9468ca30: ?? at ??:0
Frame 4: 0x7f9468879c: ?? at ??:0
Frame 5: 0x7f9468bef8: ?? at ??:0
Frame 6: 0x7f945d602c: ?? at ??:0
Frame 7: 0x7f946aacc8: ?? at ??:0

allocations_size: 222608 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f9468c810: ?? at ??:0
Frame 3: 0x7f9468ca00: ?? at ??:0
Frame 4: 0x7f9468879c: ?? at ??:0
Frame 5: 0x7f9468bef8: ?? at ??:0
Frame 6: 0x7f945d602c: ?? at ??:0
Frame 7: 0x7f946aacc8: ?? at ??:0

allocations_size: 222608 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f9468c810: ?? at ??:0
Frame 3: 0x7f9468c9d0: ?? at ??:0
Frame 4: 0x7f9468879c: ?? at ??:0
Frame 5: 0x7f9468bef8: ?? at ??:0
Frame 6: 0x7f945d602c: ?? at ??:0
Frame 7: 0x7f946aacc8: ?? at ??:0

allocations_size: 222608 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f9468c810: ?? at ??:0
Frame 3: 0x7f9468c9a0: ?? at ??:0
Frame 4: 0x7f9468879c: ?? at ??:0
Frame 5: 0x7f9468bef8: ?? at ??:0
Frame 6: 0x7f945d602c: ?? at ??:0
Frame 7: 0x7f946aacc8: ?? at ??:0

allocations_size: 222608 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f9468c810: ?? at ??:0
Frame 3: 0x7f9468ca24: ?? at ??:0
Frame 4: 0x7f9468879c: ?? at ??:0
Frame 5: 0x7f9468bef8: ?? at ??:0
Frame 6: 0x7f945d602c: ?? at ??:0
Frame 7: 0x7f946aacc8: ?? at ??:0

allocations_size: 208968 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9471f28c: ?? at ??:0
Frame 2: 0x7f9471e530: ?? at ??:0
Frame 3: 0x7f9471e830: ?? at ??:0
Frame 4: 0x7f946ac3a4: ?? at ??:0
Frame 5: 0x7f9c886050: EGLHelper::initEGL(int, int) at ??:0
Frame 6: 0x7f9c88f13c: ?? at ??:0
Frame 7: 0x7f9c89325c: std::_Function_handler<void (), IntRetMsg::IntRetMsg(std::function<int ()>)::'lambda'()>::_M_invoke(std::_Any_data const&) at ??:0

allocations_size: 208968 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9471f28c: ?? at ??:0
Frame 2: 0x7f9471e530: ?? at ??:0
Frame 3: 0x7f9471e47c: ?? at ??:0
Frame 4: 0x7f946ac420: ?? at ??:0
Frame 5: 0x556b761cac: ?? at ??:0
Frame 6: 0x556b75ac9c: ?? at ??:0
Frame 7: 0x556b758d08: ?? at ??:0

allocations_size: 206496 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f958b00b0: ?? at ??:0
Frame 3: 0x7f958918f0: ?? at ??:0
Frame 4: 0x7f958923e4: ?? at ??:0
Frame 5: 0x7f947449c4: ?? at ??:0
Frame 6: 0x7f9473c348: ?? at ??:0
Frame 7: 0x7f9462335c: ?? at ??:0

allocations_size: 202240 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x556b809608: ?? at ??:0
Frame 3: 0x556b9c988c: ?? at ??:0
Frame 4: 0x556b9b1534: ?? at ??:0
Frame 5: 0x556b7b8400: ?? at ??:0
Frame 6: 0x556b81b994: ?? at ??:0
Frame 7: 0x556b686bc4: ?? at ??:0

allocations_size: 201808 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9aee884c: operator new(unsigned long, std::nothrow_t const&) at ??:0
Frame 3: 0x7f94687408: ?? at ??:0
Frame 4: 0x7f9468ad4c: ?? at ??:0
Frame 5: 0x7f94681144: ?? at ??:0
Frame 6: 0x7f94681af4: ?? at ??:0
Frame 7: 0x7f946198f8: ?? at ??:0

allocations_size: 196608 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95948c8c: ?? at ??:0
Frame 2: 0x7f95946af4: ?? at ??:0
Frame 3: 0x7f959471d8: ?? at ??:0
Frame 4: 0x7f959444ec: ?? at ??:0
Frame 5: 0x7f959409dc: ?? at ??:0
Frame 6: 0x7f95926c18: ?? at ??:0
Frame 7: 0x7f9466d684: ?? at ??:0

allocations_size: 196608 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9863e36c: void std::vector<unsigned short, std::allocator<unsigned short>>::_M_realloc_insert<unsigned short const&>(__gnu_cxx::__normal_iterator<unsigned short*, std::vector<unsigned short, std::allocator<unsigned short>>>, unsigned short const&) at ??:0
Frame 3: 0x7f9869e268: pc::factory::SV3DUpdateVisitor::assignVertices(pc::factory::SV3DNode*, std::vector<unsigned char, std::allocator<unsigned char>> const&) at ??:0
Frame 4: 0x7f9869e6d0: pc::factory::SV3DUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 5: 0x7f986804c8: pc::factory::FloorUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 6: 0x7f986444b8: pc::assets::NoCamBorderUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 7: 0x7f9869d450: pc::factory::SV3DUpdateVisitor::apply(osg::Group&) at ??:0

allocations_size: 189744 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f990840c8: ?? at ??:0
Frame 3: 0x7f99085a7c: ReaderWriterPNG::readImage(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) const at ??:0
Frame 4: 0x7f98feb0f8: osgDB::Registry::ReadImageFunctor::doRead(osgDB::ReaderWriter&) const at ??:0
Frame 5: 0x7f98fe3b00: osgDB::Registry::read(osgDB::Registry::ReadFunctor const&) at ??:0
Frame 6: 0x7f98fe499c: osgDB::Registry::readImplementation(osgDB::Registry::ReadFunctor const&, osgDB::Options::CacheHintOptions) at ??:0
Frame 7: 0x7f98fe5130: osgDB::Registry::readImageImplementation(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) at ??:0

allocations_size: 188800 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f990840c8: ?? at ??:0
Frame 3: 0x7f99085a7c: ReaderWriterPNG::readImage(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) const at ??:0
Frame 4: 0x7f98feb0f8: osgDB::Registry::ReadImageFunctor::doRead(osgDB::ReaderWriter&) const at ??:0
Frame 5: 0x7f98fe3b00: osgDB::Registry::read(osgDB::Registry::ReadFunctor const&) at ??:0
Frame 6: 0x7f98fe499c: osgDB::Registry::readImplementation(osgDB::Registry::ReadFunctor const&, osgDB::Options::CacheHintOptions) at ??:0
Frame 7: 0x7f98fe5130: osgDB::Registry::readImageImplementation(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) at ??:0

allocations_size: 188496 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9863d3e4: pc::assets::BlurredFloorPlate::extractStretchGeometry(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool, std::vector<unsigned short, std::allocator<unsigned short>> const&, std::vector<unsigned char, std::allocator<unsigned char>> const&, osg::Vec2f const&, osg::Vec2f const&) at ??:0
Frame 3: 0x7f98642e70: pc::assets::NoCamBorderUpdateVisitor::createBaseplateAndTextureStretchGeometry(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool, std::vector<unsigned short, std::allocator<unsigned short>> const&) at ??:0
Frame 4: 0x7f986448d4: pc::assets::NoCamBorderUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 5: 0x7f9869d450: pc::factory::SV3DUpdateVisitor::apply(osg::Group&) at ??:0
Frame 6: 0x7f98640228: pc::assets::FloorPlateRenderer::FloorPlateRenderer(pc::factory::Floor*, pc::worker::bowlshaping::PolarBowlLayout const&, pc::texfloor::core::FloorGenerator*, pc::assets::FloorPlateStateHandler*, cc::core::AssetId, pc::core::Framework*, bool) at ??:0
Frame 7: 0x7f98782c44: cc::assets::common::CustomFloorPlateRenderer::CustomFloorPlateRenderer(pc::factory::Floor*, pc::worker::bowlshaping::PolarBowlLayout const&, pc::texfloor::core::FloorGenerator*, cc::core::AssetId, pc::core::Framework*, bool) at ??:0

allocations_size: 187024 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f95bdaeb0: ?? at ??:0
Frame 3: 0x7f95b5cf40: ?? at ??:0
Frame 4: 0x7f959578b8: ?? at ??:0
Frame 5: 0x7f9595bd60: ?? at ??:0
Frame 6: 0x7f959803a8: ?? at ??:0
Frame 7: 0x7f95980b00: ?? at ??:0

allocations_size: 187024 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f95bdaeb0: ?? at ??:0
Frame 3: 0x7f95b59f6c: ?? at ??:0
Frame 4: 0x7f959578b8: ?? at ??:0
Frame 5: 0x7f9595bd60: ?? at ??:0
Frame 6: 0x7f959803a8: ?? at ??:0
Frame 7: 0x7f959801ac: ?? at ??:0

allocations_size: 184320 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95948c8c: ?? at ??:0
Frame 2: 0x7f95946af4: ?? at ??:0
Frame 3: 0x7f959471d8: ?? at ??:0
Frame 4: 0x7f9594453c: ?? at ??:0
Frame 5: 0x7f95942d40: ?? at ??:0
Frame 6: 0x7f95942858: ?? at ??:0
Frame 7: 0x7f9468ba10: ?? at ??:0

allocations_size: 182160 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x556b893900: ?? at ??:0
Frame 3: 0x556b8943e8: ?? at ??:0
Frame 4: 0x556b710670: ?? at ??:0
Frame 5: 0x556b710ec8: ?? at ??:0
Frame 6: 0x556b7107dc: ?? at ??:0
Frame 7: 0x556b710ec8: ?? at ??:0

allocations_size: 180224 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95948c8c: ?? at ??:0
Frame 2: 0x7f95946af4: ?? at ??:0
Frame 3: 0x7f959471d8: ?? at ??:0
Frame 4: 0x7f959444ec: ?? at ??:0
Frame 5: 0x7f95942d40: ?? at ??:0
Frame 6: 0x7f95942858: ?? at ??:0
Frame 7: 0x7f9468ba10: ?? at ??:0

allocations_size: 172032 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95948c8c: ?? at ??:0
Frame 2: 0x7f95946af4: ?? at ??:0
Frame 3: 0x7f959471d8: ?? at ??:0
Frame 4: 0x7f9594453c: ?? at ??:0
Frame 5: 0x7f9593f8b4: ?? at ??:0
Frame 6: 0x7f95941030: ?? at ??:0
Frame 7: 0x7f9468bc80: ?? at ??:0

allocations_size: 155648 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9863e36c: void std::vector<unsigned short, std::allocator<unsigned short>>::_M_realloc_insert<unsigned short const&>(__gnu_cxx::__normal_iterator<unsigned short*, std::vector<unsigned short, std::allocator<unsigned short>>>, unsigned short const&) at ??:0
Frame 3: 0x7f98fff030: osgDB::InputStream::readPrimitiveSet() at ??:0
Frame 4: 0x7f9918d41c: osgDB::ListSerializer<osg::Geometry, std::vector<osg::ref_ptr<osg::PrimitiveSet>, std::allocator<osg::ref_ptr<osg::PrimitiveSet>>>>::read(osgDB::InputStream&, osg::Object&) at ??:0
Frame 5: 0x7f98ff3608: osgDB::ObjectWrapper::read(osgDB::InputStream&, osg::Object&) at ??:0
Frame 6: 0x7f98ffd5a4: osgDB::InputStream::readObjectFields(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, unsigned int, osg::Object*) at ??:0
Frame 7: 0x7f98ffe6ac: osgDB::InputStream::readObject(osg::Object*) at ??:0

allocations_size: 144992 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f990840c8: ?? at ??:0
Frame 3: 0x7f99085a7c: ReaderWriterPNG::readImage(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) const at ??:0
Frame 4: 0x7f98feb0f8: osgDB::Registry::ReadImageFunctor::doRead(osgDB::ReaderWriter&) const at ??:0
Frame 5: 0x7f98fe3b00: osgDB::Registry::read(osgDB::Registry::ReadFunctor const&) at ??:0
Frame 6: 0x7f98fe499c: osgDB::Registry::readImplementation(osgDB::Registry::ReadFunctor const&, osgDB::Options::CacheHintOptions) at ??:0
Frame 7: 0x7f98fe5130: osgDB::Registry::readImageImplementation(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) at ??:0

allocations_size: 144256 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f990840c8: ?? at ??:0
Frame 3: 0x7f99085a7c: ReaderWriterPNG::readImage(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) const at ??:0
Frame 4: 0x7f98feb0f8: osgDB::Registry::ReadImageFunctor::doRead(osgDB::ReaderWriter&) const at ??:0
Frame 5: 0x7f98fe3b00: osgDB::Registry::read(osgDB::Registry::ReadFunctor const&) at ??:0
Frame 6: 0x7f98fe499c: osgDB::Registry::readImplementation(osgDB::Registry::ReadFunctor const&, osgDB::Options::CacheHintOptions) at ??:0
Frame 7: 0x7f98fe5130: osgDB::Registry::readImageImplementation(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) at ??:0

allocations_size: 143088 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f98624db0: std::vector<osg::Vec3f, std::allocator<osg::Vec3f>>::_M_fill_insert(__gnu_cxx::__normal_iterator<osg::Vec3f*, std::vector<osg::Vec3f, std::allocator<osg::Vec3f>>>, unsigned long, osg::Vec3f const&) at ??:0
Frame 3: 0x7f9900e894: void osgDB::InputStream::readArrayImplementation<osg::TemplateArray<osg::Vec3f, (osg::Array::Type)28, 3, 5126>>(osg::TemplateArray<osg::Vec3f, (osg::Array::Type)28, 3, 5126>*, unsigned int, unsigned int) at ??:0
Frame 4: 0x7f99001afc: osgDB::InputStream::readArray() at ??:0
Frame 5: 0x7f99187a04: ?? at ??:0
Frame 6: 0x7f9918c4f0: ?? at ??:0
Frame 7: 0x7f98ff3608: osgDB::ObjectWrapper::read(osgDB::InputStream&, osg::Object&) at ??:0

allocations_size: 136320 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f98624db0: std::vector<osg::Vec3f, std::allocator<osg::Vec3f>>::_M_fill_insert(__gnu_cxx::__normal_iterator<osg::Vec3f*, std::vector<osg::Vec3f, std::allocator<osg::Vec3f>>>, unsigned long, osg::Vec3f const&) at ??:0
Frame 3: 0x7f9900e894: void osgDB::InputStream::readArrayImplementation<osg::TemplateArray<osg::Vec3f, (osg::Array::Type)28, 3, 5126>>(osg::TemplateArray<osg::Vec3f, (osg::Array::Type)28, 3, 5126>*, unsigned int, unsigned int) at ??:0
Frame 4: 0x7f99001afc: osgDB::InputStream::readArray() at ??:0
Frame 5: 0x7f99187a04: ?? at ??:0
Frame 6: 0x7f9918c4f0: ?? at ??:0
Frame 7: 0x7f98ff3608: osgDB::ObjectWrapper::read(osgDB::InputStream&, osg::Object&) at ??:0

allocations_size: 134304 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f98624db0: std::vector<osg::Vec3f, std::allocator<osg::Vec3f>>::_M_fill_insert(__gnu_cxx::__normal_iterator<osg::Vec3f*, std::vector<osg::Vec3f, std::allocator<osg::Vec3f>>>, unsigned long, osg::Vec3f const&) at ??:0
Frame 3: 0x7f9900e894: void osgDB::InputStream::readArrayImplementation<osg::TemplateArray<osg::Vec3f, (osg::Array::Type)28, 3, 5126>>(osg::TemplateArray<osg::Vec3f, (osg::Array::Type)28, 3, 5126>*, unsigned int, unsigned int) at ??:0
Frame 4: 0x7f99001afc: osgDB::InputStream::readArray() at ??:0
Frame 5: 0x7f99187a04: ?? at ??:0
Frame 6: 0x7f9918c4f0: ?? at ??:0
Frame 7: 0x7f98ff3608: osgDB::ObjectWrapper::read(osgDB::InputStream&, osg::Object&) at ??:0

allocations_size: 131456 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x556b713334: ?? at ??:0
Frame 3: 0x556b713408: ?? at ??:0
Frame 4: 0x556b713408: ?? at ??:0
Frame 5: 0x556b713408: ?? at ??:0
Frame 6: 0x556b713408: ?? at ??:0
Frame 7: 0x556b713408: ?? at ??:0

allocations_size: 131216 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f98bd4f30: cc::worker::core::CustomTaskManager::OnInit() at ??:0
Frame 3: 0x7f985e3560: pc::core::ICyclicRunnable::run() at ??:0
Frame 4: 0x7f9af1502c: ?? at ??:0
Frame 5: 0x7f9accec5c: pthread_create at ??:0
Frame 6: 0x7f9ad2ea4c: epoll_pwait2 at ??:0

allocations_size: 131072 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95948c8c: ?? at ??:0
Frame 2: 0x7f95946af4: ?? at ??:0
Frame 3: 0x7f959471d8: ?? at ??:0
Frame 4: 0x7f959444ec: ?? at ??:0
Frame 5: 0x7f959409dc: ?? at ??:0
Frame 6: 0x7f945d1674: ?? at ??:0
Frame 7: 0x7f945d12f0: ?? at ??:0

allocations_size: 131072 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95948c8c: ?? at ??:0
Frame 2: 0x7f95946af4: ?? at ??:0
Frame 3: 0x7f959471d8: ?? at ??:0
Frame 4: 0x7f959444ec: ?? at ??:0
Frame 5: 0x7f959409dc: ?? at ??:0
Frame 6: 0x7f95927364: ?? at ??:0
Frame 7: 0x7f946a4c70: ?? at ??:0

allocations_size: 131072 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95948c8c: ?? at ??:0
Frame 2: 0x7f95946af4: ?? at ??:0
Frame 3: 0x7f959471d8: ?? at ??:0
Frame 4: 0x7f959444ec: ?? at ??:0
Frame 5: 0x7f959409dc: ?? at ??:0
Frame 6: 0x7f9466a6c8: ?? at ??:0
Frame 7: 0x7f9461c948: ?? at ??:0

allocations_size: 131072 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95950a40: ?? at ??:0
Frame 2: 0x7f958eff24: ?? at ??:0
Frame 3: 0x7f946886a4: ?? at ??:0
Frame 4: 0x7f9468bef8: ?? at ??:0
Frame 5: 0x7f945d602c: ?? at ??:0
Frame 6: 0x7f946aacc8: ?? at ??:0
Frame 7: 0x7f9c886488: EGLHelper::initEGL(int, int) at ??:0

allocations_size: 131072 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95948c8c: ?? at ??:0
Frame 2: 0x7f95946af4: ?? at ??:0
Frame 3: 0x7f959471d8: ?? at ??:0
Frame 4: 0x7f959444ec: ?? at ??:0
Frame 5: 0x7f959409dc: ?? at ??:0
Frame 6: 0x7f946524fc: ?? at ??:0
Frame 7: 0x7f9466865c: ?? at ??:0

allocations_size: 131072 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95948c8c: ?? at ??:0
Frame 2: 0x7f95946af4: ?? at ??:0
Frame 3: 0x7f959471d8: ?? at ??:0
Frame 4: 0x7f959444ec: ?? at ??:0
Frame 5: 0x7f959409dc: ?? at ??:0
Frame 6: 0x7f94637248: ?? at ??:0
Frame 7: 0x7f946356c4: ?? at ??:0

allocations_size: 131072 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95948c8c: ?? at ??:0
Frame 2: 0x7f95946af4: ?? at ??:0
Frame 3: 0x7f959471d8: ?? at ??:0
Frame 4: 0x7f959444ec: ?? at ??:0
Frame 5: 0x7f959409dc: ?? at ??:0
Frame 6: 0x7f95926c18: ?? at ??:0
Frame 7: 0x7f94652988: ?? at ??:0

allocations_size: 131072 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9863e36c: void std::vector<unsigned short, std::allocator<unsigned short>>::_M_realloc_insert<unsigned short const&>(__gnu_cxx::__normal_iterator<unsigned short*, std::vector<unsigned short, std::allocator<unsigned short>>>, unsigned short const&) at ??:0
Frame 3: 0x7f98fff030: osgDB::InputStream::readPrimitiveSet() at ??:0
Frame 4: 0x7f9918d41c: osgDB::ListSerializer<osg::Geometry, std::vector<osg::ref_ptr<osg::PrimitiveSet>, std::allocator<osg::ref_ptr<osg::PrimitiveSet>>>>::read(osgDB::InputStream&, osg::Object&) at ??:0
Frame 5: 0x7f98ff3608: osgDB::ObjectWrapper::read(osgDB::InputStream&, osg::Object&) at ??:0
Frame 6: 0x7f98ffd5a4: osgDB::InputStream::readObjectFields(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, unsigned int, osg::Object*) at ??:0
Frame 7: 0x7f98ffe6ac: osgDB::InputStream::readObject(osg::Object*) at ??:0

allocations_size: 131072 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9863e36c: void std::vector<unsigned short, std::allocator<unsigned short>>::_M_realloc_insert<unsigned short const&>(__gnu_cxx::__normal_iterator<unsigned short*, std::vector<unsigned short, std::allocator<unsigned short>>>, unsigned short const&) at ??:0
Frame 3: 0x7f9869e268: pc::factory::SV3DUpdateVisitor::assignVertices(pc::factory::SV3DNode*, std::vector<unsigned char, std::allocator<unsigned char>> const&) at ??:0
Frame 4: 0x7f9869e6d0: pc::factory::SV3DUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 5: 0x7f9871f12c: pc::worker::bowlshaping::BowlUpdateVisitor::update(pc::factory::SV3DNode*, pc::core::DirectionalExtArray<pc::c2w::SatCam> const&, std::array<pc::factory::Mask, 6ul> const&, bool, bool) at ??:0
Frame 6: 0x7f9869d450: pc::factory::SV3DUpdateVisitor::apply(osg::Group&) at ??:0
Frame 7: 0x7f98749090: cc::assets::common::Bowl::Bowl(cc::core::AssetId, pc::core::Framework*, pc::worker::bowlshaping::BowlLayoutGenerator*) at ??:0

allocations_size: 131072 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95948c8c: ?? at ??:0
Frame 2: 0x7f95946af4: ?? at ??:0
Frame 3: 0x7f959471d8: ?? at ??:0
Frame 4: 0x7f959444ec: ?? at ??:0
Frame 5: 0x7f959409dc: ?? at ??:0
Frame 6: 0x7f94668444: ?? at ??:0
Frame 7: 0x7f9466ab84: ?? at ??:0

allocations_size: 131072 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95950a40: ?? at ??:0
Frame 2: 0x7f958eff24: ?? at ??:0
Frame 3: 0x7f946886a4: ?? at ??:0
Frame 4: 0x7f9468bef8: ?? at ??:0
Frame 5: 0x7f945d602c: ?? at ??:0
Frame 6: 0x7f946aacc8: ?? at ??:0
Frame 7: 0x556b7614e8: ?? at ??:0

allocations_size: 125508 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f98624db0: std::vector<osg::Vec3f, std::allocator<osg::Vec3f>>::_M_fill_insert(__gnu_cxx::__normal_iterator<osg::Vec3f*, std::vector<osg::Vec3f, std::allocator<osg::Vec3f>>>, unsigned long, osg::Vec3f const&) at ??:0
Frame 3: 0x7f9900e894: void osgDB::InputStream::readArrayImplementation<osg::TemplateArray<osg::Vec3f, (osg::Array::Type)28, 3, 5126>>(osg::TemplateArray<osg::Vec3f, (osg::Array::Type)28, 3, 5126>*, unsigned int, unsigned int) at ??:0
Frame 4: 0x7f99001afc: osgDB::InputStream::readArray() at ??:0
Frame 5: 0x7f99187a04: ?? at ??:0
Frame 6: 0x7f9918c4f0: ?? at ??:0
Frame 7: 0x7f98ff3608: osgDB::ObjectWrapper::read(osgDB::InputStream&, osg::Object&) at ??:0

allocations_size: 118784 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9863e36c: void std::vector<unsigned short, std::allocator<unsigned short>>::_M_realloc_insert<unsigned short const&>(__gnu_cxx::__normal_iterator<unsigned short*, std::vector<unsigned short, std::allocator<unsigned short>>>, unsigned short const&) at ??:0
Frame 3: 0x7f98fff030: osgDB::InputStream::readPrimitiveSet() at ??:0
Frame 4: 0x7f9918d41c: osgDB::ListSerializer<osg::Geometry, std::vector<osg::ref_ptr<osg::PrimitiveSet>, std::allocator<osg::ref_ptr<osg::PrimitiveSet>>>>::read(osgDB::InputStream&, osg::Object&) at ??:0
Frame 5: 0x7f98ff3608: osgDB::ObjectWrapper::read(osgDB::InputStream&, osg::Object&) at ??:0
Frame 6: 0x7f98ffd5a4: osgDB::InputStream::readObjectFields(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, unsigned int, osg::Object*) at ??:0
Frame 7: 0x7f98ffe6ac: osgDB::InputStream::readObject(osg::Object*) at ??:0

allocations_size: 116160 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f958a9800: ?? at ??:0
Frame 2: 0x7f958b5304: ?? at ??:0
Frame 3: 0x7f958b55ac: ?? at ??:0
Frame 4: 0x7f958c8a98: ?? at ??:0
Frame 5: 0x7f958b6994: ?? at ??:0
Frame 6: 0x7f958a92f0: ?? at ??:0
Frame 7: 0x7f946770fc: ?? at ??:0

allocations_size: 113256 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x556b67984c: ?? at ??:0
Frame 3: 0x556b67a6f8: ?? at ??:0
Frame 4: 0x556b710670: ?? at ??:0
Frame 5: 0x556b710ec8: ?? at ??:0
Frame 6: 0x556b7107dc: ?? at ??:0
Frame 7: 0x556b710ec8: ?? at ??:0

allocations_size: 111800 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9b4827dc: g_malloc at ??:0
Frame 2: 0x7f9b2a0818: gst_event_new_custom at ??:0
Frame 3: 0x7f4c7499bc: ?? at ??:0
Frame 4: 0x7f9b29b664: gst_element_change_state at ??:0
Frame 5: 0x7f9b29cce0: ?? at ??:0
Frame 6: 0x7f9b271860: ?? at ??:0
Frame 7: 0x7f9b2c88f4: ?? at ??:0

allocations_size: 111800 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9b4827dc: g_malloc at ??:0
Frame 2: 0x7f9b2a0818: gst_event_new_custom at ??:0
Frame 3: 0x7f9b1fd1f0: ?? at ??:0
Frame 4: 0x7f93b35a94: ?? at ??:0
Frame 5: 0x7f9b1fcc8c: ?? at ??:0
Frame 6: 0x7f93b370f0: ?? at ??:0
Frame 7: 0x7f9b2c2bd4: ?? at ??:0

allocations_size: 111800 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9b4827dc: g_malloc at ??:0
Frame 2: 0x7f9b2a0818: gst_event_new_custom at ??:0
Frame 3: 0x7f93b4bb5c: ?? at ??:0
Frame 4: 0x7f93b49f04: ?? at ??:0
Frame 5: 0x7f4c4ace1c: ?? at ??:0
Frame 6: 0x7f9b2c2bd4: ?? at ??:0
Frame 7: 0x7f9b2c2014: ?? at ??:0

allocations_size: 110592 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f95948c8c: ?? at ??:0
Frame 2: 0x7f95946af4: ?? at ??:0
Frame 3: 0x7f959471d8: ?? at ??:0
Frame 4: 0x7f9594453c: ?? at ??:0
Frame 5: 0x7f95943674: ?? at ??:0
Frame 6: 0x7f95943478: ?? at ??:0
Frame 7: 0x7f9594034c: ?? at ??:0

allocations_size: 106672 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f990840c8: ?? at ??:0
Frame 3: 0x7f99085a7c: ReaderWriterPNG::readImage(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) const at ??:0
Frame 4: 0x7f98feb0f8: osgDB::Registry::ReadImageFunctor::doRead(osgDB::ReaderWriter&) const at ??:0
Frame 5: 0x7f98fe3b00: osgDB::Registry::read(osgDB::Registry::ReadFunctor const&) at ??:0
Frame 6: 0x7f98fe499c: osgDB::Registry::readImplementation(osgDB::Registry::ReadFunctor const&, osgDB::Options::CacheHintOptions) at ??:0
Frame 7: 0x7f98fe5130: osgDB::Registry::readImageImplementation(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, osgDB::Options const*) at ??:0

allocations_size: 106344 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9873e820: osg::MatrixTransform::clone(osg::CopyOp const&) const at ??:0
Frame 3: 0x7f98df9670: osg::Node* osg::clone<osg::Node>(osg::Node const*, osg::CopyOp const&) at ??:0
Frame 4: 0x7f98e3c664: osg::Group::Group(osg::Group const&, osg::CopyOp const&) at ??:0
Frame 5: 0x7f98ea42bc: osg::Transform::Transform(osg::Transform const&, osg::CopyOp const&) at ??:0
Frame 6: 0x7f98e48438: osg::MatrixTransform::MatrixTransform(osg::MatrixTransform const&, osg::CopyOp const&) at ??:0
Frame 7: 0x7f9873e830: osg::MatrixTransform::clone(osg::CopyOp const&) const at ??:0

allocations_size: 105600 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x556b695534: ?? at ??:0
Frame 3: 0x556b699d18: ?? at ??:0
Frame 4: 0x556b69a4bc: ?? at ??:0
Frame 5: 0x556b76b364: ?? at ??:0
Frame 6: 0x556b6ab5b4: ?? at ??:0
Frame 7: 0x556b6ab8f0: ?? at ??:0

allocations_size: 104832 B
Frame 0: 0x7f9cd92188: malloc at /usr/src/debug/malloc-debug15/1.0/malloc_debug_mtk.c:0
Frame 1: 0x7f9aee87ec: operator new(unsigned long) at ??:0
Frame 2: 0x7f9873e874: osg::MatrixTransform::cloneType() const at ??:0
Frame 3: 0x7f98ffd798: osgDB::InputStream::readObjectFields(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, unsigned int, osg::Object*) at ??:0
Frame 4: 0x7f98ffe6ac: osgDB::InputStream::readObject(osg::Object*) at ??:0
Frame 5: 0x7f9918e534: ?? at ??:0
Frame 6: 0x7f98ff3608: osgDB::ObjectWrapper::read(osgDB::InputStream&, osg::Object&) at ??:0
Frame 7: 0x7f98ffd5a4: osgDB::InputStream::readObjectFields(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char>> const&, unsigned int, osg::Object*) at ??:0

