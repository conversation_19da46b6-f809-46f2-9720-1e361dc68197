#ifndef RENDERMANAGERBASEIMPL_H
#define RENDERMANAGERBASEIMPL_H

#include <memory>
#include <vector>
#include "RenderManagerBase.h"
#include "RenderConfig.h"


#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/sm/viewmode/inc/ViewModeStateMachine.h"
//#include "cc/fdbus/inc/fdbus.hpp"
#include "cc/worker/core/inc/CustomTaskManager.h"
#include "cc/util/logging/inc/LoggingContexts.h"
//#include "cc/cpc/inc/defines.hpp"

//#include "cc/customsocket/inc/CustomServerSocket.h"
#include "hw/version/inc/Version.h"

#include "pc/generic/util/cli/inc/CommandLineInterface.h"
#include "pc/generic/util/coding/inc/ReaderWriter.h"
#include "pc/generic/util/com/inc/ServerSocket.h"
#include "pc/generic/util/chrono/inc/chrono.h"
#include "pc/svs/c2w/inc/CalibrationReader.h"
#include "pc/svs/daddy/inc/BaseDaddyPorts.h"
//#include "cc/imc/qualcomm/qcengine/inc/QcarCamMux.h"
//#include "cc/imc/qualcomm/qcengine/inc/QcEngine.h"
//#include "cc/imc/qualcomm/qcengine/inc/QcarCamDaddyPorts.h"
#include "pc/svs/util/logging/inc/ReadFileLogger.h"
#include "pc/svs/util/logging/inc/SV3DNotifyHandler.h"
#include "pc/svs/worker/core/inc/TaskManager.h"
//#include "cc/target/qualcomm/inc/QcHuCoordinator.h"
//#include "cc/target/qualcomm/inc/CustomInterfaceSync.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
//#include "cc/mod/inc/ModManager.hpp"

//#include "cc/core/inc/CustomParamData.h"

#include "CustomSystemConf.h"

#include <cassert>
#include <csignal>
#include <unistd.h>
#include <sys/stat.h>
#include <getopt.h>

#include <osgDB/Registry>
#include <osgDB/FileUtils>

/*** hnd2hi ***/
//#include "cc/imc/qualcomm/qcengine/inc/QcGraphicsContext.h"
//#include "cc/target/qualcomm/inc/common_svs_interface.hpp"


#include <cassert>
#include <csignal>
#include <unistd.h>
#include <sys/stat.h>
#include <getopt.h>
#include <iostream>


#include <osgDB/Registry>
#include <osgDB/FileUtils>

#include "thread"

#include "cc/target/linux/inc/QcEngine.h"
#include "cc/target/linux/inc/ServiceBase.h"

// Forward declarations for external linkage objects and functions
extern std::string g_prefixPath;
extern int g_keepRunning;


// C interface function declarations
extern "C" {
    ServiceBase* createComLinuxUserInstance();
    RenderManagerBase* createRenderManager();
    int releaseRenderManager(RenderManagerBase* f_RenderManager);
}

class RenderManagerBaseImpl : public RenderManagerBase {
public:
    RenderManagerBaseImpl();
    virtual ~RenderManagerBaseImpl();

    int init(std::shared_ptr<RenderConfig> renderConfig) override;
    int deInit() override;
    int drawCameraTexture(int cameraIdentifies[], int size) override;
    int drawWindow(int x, int y, int width, int height) override;
    int getCameraTextureId(int identify) override;
    int setCameraTextureId(int textureId[], int size) override;
    int setFrameBuffer(unsigned int fbo);// override {return 0;}
    // void setTouchEvent(MotionEvent event) override;

private:
    bool initialized;
    std::vector<int> m_textureIds;

    
    //OsgMainApp mainApp;

    // mainApp.initOsgWindow(0, 0, 1920, 1080);

    // //std::string modelpathname = "./vehicle_model/vehicle.osg";
    // mainApp.loadModels();

    // for (int loop = 0; loop < 600; loop++)
    // {
    //     mainApp.draw();
    // }

    std::thread *mEngThread;
  
};

#endif // RENDERMANAGERBASEIMPL_H